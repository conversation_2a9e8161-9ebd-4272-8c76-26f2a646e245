"""
延迟分析接口模块
提供任务延迟相关指标的计算和分析
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
from enum import Enum
import logging

from src.env.physics_layer.task_tracking import TaskTrackingRecord, TaskStatus
from src.env.satellite_cloud.compute_models import ComputeTask as SatelliteTask


class LatencyType(Enum):
    """延迟类型枚举"""
    END_TO_END = "end_to_end"
    QUEUING = "queuing"
    PROCESSING = "processing"
    TRANSMISSION = "transmission"
    RETRANSMISSION = "retransmission"


@dataclass
class LatencyMetrics:
    """延迟指标数据类"""
    mean: float
    median: float
    std: float
    min: float
    max: float
    p50: float
    p90: float
    p99: float
    cv: float  # 变异系数


class LatencyAnalyzer:
    """延迟分析器"""
    
    def __init__(self, config_path: str):
        """
        初始化延迟分析器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
    def analyze_task_records(self, task_records: List[TaskTrackingRecord], 
                            time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        分析任务记录的延迟指标
        
        Args:
            task_records: 任务跟踪记录列表
            time_window: 时间窗口(开始时间, 结束时间)，单位秒
            
        Returns:
            延迟分析结果字典
        """
        if not task_records:
            return self._empty_metrics()
        
        # 过滤时间窗口内的记录
        if time_window:
            start_time, end_time = time_window
            filtered_records = [
                r for r in task_records 
                if start_time <= r.generation_time <= end_time
            ]
        else:
            filtered_records = task_records
        
        # 只分析已完成的任务
        completed_records = [
            r for r in filtered_records 
            if r.status == TaskStatus.DELIVERED
        ]
        
        if not completed_records:
            return self._empty_metrics()
        
        # 计算各种延迟
        latencies = {
            'end_to_end': [],
            'queuing': [],
            'processing': [],
            'transmission': [],
            'retransmission': []
        }
        
        for record in completed_records:
            # 端到端延迟
            e2e_latency = self._calculate_end_to_end_latency(record)
            if e2e_latency is not None:
                latencies['end_to_end'].append(e2e_latency)
            
            # 排队延迟
            queuing_latency = self._calculate_queuing_latency(record)
            if queuing_latency is not None:
                latencies['queuing'].append(queuing_latency)
            
            # 处理延迟
            processing_latency = self._calculate_processing_latency(record)
            if processing_latency is not None:
                latencies['processing'].append(processing_latency)
            
            # 传输延迟
            trans_latency = self._calculate_transmission_latency(record)
            if trans_latency is not None:
                latencies['transmission'].append(trans_latency)
            
            # 重传延迟
            retrans_latency = self._calculate_retransmission_latency(record)
            if retrans_latency is not None:
                latencies['retransmission'].append(retrans_latency)
        
        # 计算统计指标
        results = {}
        for latency_type, values in latencies.items():
            if values:
                results[f'{latency_type}_latency'] = self._calculate_statistics(values)
        
        # 计算归一化指标
        results['normalized_metrics'] = self._calculate_normalized_metrics(
            completed_records, latencies
        )
        
        return results
    
    def analyze_satellite_tasks(self, satellite_tasks: List[List[SatelliteTask]],
                               time_window: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        分析卫星任务的延迟指标
        
        Args:
            satellite_tasks: 每个卫星的任务列表
            time_window: 时间窗口
            
        Returns:
            延迟分析结果
        """
        all_tasks = []
        for sat_tasks in satellite_tasks:
            all_tasks.extend(sat_tasks)
        
        if not all_tasks:
            return self._empty_metrics()
        
        # 过滤时间窗口和已完成任务
        if time_window:
            start_time, end_time = time_window
            filtered_tasks = [
                t for t in all_tasks 
                if start_time <= t.arrival_time <= end_time
            ]
        else:
            filtered_tasks = all_tasks
        
        completed_tasks = [
            t for t in filtered_tasks 
            if t.status.value == 'completed'
        ]
        
        if not completed_tasks:
            return self._empty_metrics()
        
        # 计算延迟
        latencies = {
            'end_to_end': [],
            'queuing': [],
            'processing': [],
            'communication': []
        }
        
        for task in completed_tasks:
            # 端到端延迟
            if task.completion_time and task.arrival_time:
                e2e = task.completion_time - task.arrival_time
                latencies['end_to_end'].append(e2e)
            
            # 排队延迟
            if task.start_time and task.arrival_time:
                queuing = task.start_time - task.arrival_time
                latencies['queuing'].append(queuing)
            
            # 处理延迟
            if hasattr(task, 'processing_delay') and task.processing_delay > 0:
                latencies['processing'].append(task.processing_delay)
            
            # 通信延迟
            if hasattr(task, 'communication_delay') and task.communication_delay > 0:
                latencies['communication'].append(task.communication_delay)
        
        # 计算统计指标
        results = {}
        for latency_type, values in latencies.items():
            if values:
                results[f'{latency_type}_latency'] = self._calculate_statistics(values)
        
        # 计算任务级别的归一化指标
        if latencies['end_to_end']:
            total_latency = sum(latencies['end_to_end'])
            results['normalized_metrics'] = {
                'avg_latency_per_task': total_latency / len(completed_tasks),
                'total_completed_tasks': len(completed_tasks),
                'total_latency': total_latency
            }
        
        return results
    
    def _calculate_end_to_end_latency(self, record: TaskTrackingRecord) -> Optional[float]:
        """计算端到端延迟"""
        if record.delivery_time and record.generation_time:
            return record.delivery_time - record.generation_time
        return None
    
    def _calculate_queuing_latency(self, record: TaskTrackingRecord) -> Optional[float]:
        """计算排队延迟"""
        if record.processing_nodes and record.assignment_time:
            first_processing = min(record.processing_nodes, key=lambda x: x.start_time)
            return first_processing.start_time - record.assignment_time
        return None
    
    def _calculate_processing_latency(self, record: TaskTrackingRecord) -> Optional[float]:
        """计算处理延迟"""
        if record.processing_nodes:
            total_processing = sum(node.processing_time for node in record.processing_nodes)
            return total_processing
        return None
    
    def _calculate_transmission_latency(self, record: TaskTrackingRecord) -> Optional[float]:
        """计算传输延迟"""
        if record.processing_nodes:
            total_transmission = sum(
                node.offload_transmission_time + node.result_transmission_time 
                for node in record.processing_nodes
            )
            return total_transmission
        return None
    
    def _calculate_retransmission_latency(self, record: TaskTrackingRecord) -> Optional[float]:
        """计算重传延迟"""
        if hasattr(record, 'retransmission_count') and record.retransmission_count > 0:
            # 假设每次重传增加固定延迟
            return record.retransmission_count * 0.5  # 0.5秒每次重传
        return None
    
    def _calculate_statistics(self, values: List[float]) -> LatencyMetrics:
        """计算统计指标"""
        if not values:
            return LatencyMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        values_array = np.array(values)
        mean = np.mean(values_array)
        std = np.std(values_array)
        
        return LatencyMetrics(
            mean=mean,
            median=np.median(values_array),
            std=std,
            min=np.min(values_array),
            max=np.max(values_array),
            p50=np.percentile(values_array, 50),
            p90=np.percentile(values_array, 90),
            p99=np.percentile(values_array, 99),
            cv=std / mean if mean > 0 else 0
        )
    
    def _calculate_normalized_metrics(self, records: List[TaskTrackingRecord], 
                                     latencies: Dict) -> Dict:
        """计算归一化指标"""
        metrics = {}
        
        # 平均每任务延迟
        if latencies['end_to_end']:
            total_latency = sum(latencies['end_to_end'])
            metrics['avg_latency_per_task'] = total_latency / len(records)
            
            # 单位数据延迟
            total_data = sum(r.data_size_mb for r in records)
            if total_data > 0:
                metrics['latency_per_mb'] = total_latency / total_data
        
        return metrics
    
    def _empty_metrics(self) -> Dict:
        """返回空指标"""
        return {
            'end_to_end_latency': LatencyMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0),
            'normalized_metrics': {
                'avg_latency_per_task': 0,
                'latency_per_mb': 0
            }
        }