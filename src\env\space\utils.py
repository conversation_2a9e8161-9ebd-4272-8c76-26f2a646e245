"""
工具函数模块

提供环境中常用的辅助功能
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple


def normalize_observation(
    obs: np.ndarray,
    min_val: float = 0.0,
    max_val: float = 1.0
) -> np.ndarray:
    """
    归一化观测值到指定范围
    
    Args:
        obs: 原始观测
        min_val: 最小值
        max_val: 最大值
        
    Returns:
        归一化后的观测
    """
    if obs.size == 0:
        return obs
    
    obs_min = np.min(obs)
    obs_max = np.max(obs)
    
    if obs_max - obs_min < 1e-6:
        return np.ones_like(obs) * (min_val + max_val) / 2
    
    normalized = (obs - obs_min) / (obs_max - obs_min)
    scaled = normalized * (max_val - min_val) + min_val
    
    return scaled


def compute_regional_load(
    satellites: List,
    region_mapping: Dict[int, int]
) -> np.ndarray:
    """
    计算区域负载统计信息
    
    Args:
        satellites: 卫星列表
        region_mapping: 卫星到区域的映射
        
    Returns:
        4维区域负载向量
    """
    regional_load = np.zeros(4)
    
    # 按区域统计
    region_loads = {}
    for sat_idx, sat in enumerate(satellites):
        region_id = region_mapping.get(sat_idx, sat_idx // 18)
        if region_id not in region_loads:
            region_loads[region_id] = []
        
        cpu_util = getattr(sat, 'cpu_utilization', 0.3)
        region_loads[region_id].append(cpu_util)
    
    # 计算统计指标
    all_loads = []
    for loads in region_loads.values():
        all_loads.extend(loads)
    
    if all_loads:
        regional_load[0] = np.mean(all_loads)  # 平均CPU利用率
        regional_load[1] = np.std(all_loads)   # 标准差
        regional_load[2] = np.var(all_loads)   # 方差
        regional_load[3] = len(all_loads) / 72  # 活跃卫星比例
    
    return regional_load


def validate_config(config: Dict, schema: Dict = None, strict: bool = False) -> Tuple[bool, List[str]]:
    """
    验证配置文件格式（灵活验证）
    
    Args:
        config: 配置字典
        schema: 验证模式（可选，使用默认模式）
        strict: 是否严格模式（禁止额外字段）
        
    Returns:
        (是否有效, 错误信息列表)
    """
    errors = []
    
    # 基础配置结构（更宽松的验证）
    required_structure = schema or {
        'space': {
            'observation': {
                'max_neighbors': (int, 1, 72),
                'max_clouds': (int, 1, 10), 
                'max_tasks': (int, 1, 1000),
                'self_state_dim': (int, 1, 50),
                'neighbor_state_dim': (int, 1, 50),
                'cloud_state_dim': (int, 1, 50),
                'task_feature_dim': (int, 1, 50)
            },
            'action': {
                'max_actions': (int, 1, 100),
                'num_neighbors': (int, 1, 72),
                'num_clouds': (int, 1, 10),
                'enable_resource_allocation': (bool,)
            },
            'dynamic_action': {
                'satellite_distance_threshold_km': (float, 1, 50000),
                'cloud_distance_threshold_km': (float, 1, 50000),
                'select_nearest': (bool,)
            },
            'reward': {
                'alpha_1': (float, 0, 1000),
                'alpha_2': (float, 0, 1000),
                'alpha_3': (float, 0, 1000),
                'beta': (float, 0, 100),
                'zeta': (float, 0, 100),
                'delta': (float, 0, 100),
                'timeout_penalty': (float, -1000, 0)
            },
            'environment': {
                'max_episode_steps': (int, 1, 100000),
                'auto_reset': (bool,),
                'seed': (int, 0, 2**31 - 1)
            }
        }
    }
    
    def validate_value(value, constraint, path):
        """验证单个值"""
        try:
            if len(constraint) == 1:  # bool类型
                if not isinstance(value, constraint[0]):
                    errors.append(f"{path}: Expected {constraint[0].__name__}, got {type(value).__name__}")
                    return False
            elif len(constraint) == 3:  # 数值类型带范围
                dtype, min_val, max_val = constraint
                if not isinstance(value, dtype):
                    errors.append(f"{path}: Expected {dtype.__name__}, got {type(value).__name__}")
                    return False
                if not (min_val <= value <= max_val):
                    errors.append(f"{path}: Value {value} out of range [{min_val}, {max_val}]")
                    return False
        except Exception as e:
            errors.append(f"{path}: Validation error - {str(e)}")
            return False
        return True
    
    def validate_nested(data, structure, path=""):
        """递归验证嵌套结构"""
        valid = True
        
        # 检查必需字段
        for key, value in structure.items():
            current_path = f"{path}.{key}" if path else key
            
            if key not in data:
                errors.append(f"Missing required key: {current_path}")
                valid = False
                continue
            
            if isinstance(value, dict):
                if not isinstance(data[key], dict):
                    errors.append(f"{current_path}: Expected dict, got {type(data[key]).__name__}")
                    valid = False
                else:
                    if not validate_nested(data[key], value, current_path):
                        valid = False
            elif isinstance(value, tuple):
                if not validate_value(data[key], value, current_path):
                    valid = False
        
        # 严格模式：检查额外字段
        if strict and isinstance(data, dict):
            for key in data.keys():
                if key not in structure:
                    errors.append(f"Unexpected key in strict mode: {path}.{key}" if path else f"Unexpected key: {key}")
                    valid = False
        
        return valid
    
    is_valid = validate_nested(config, required_structure)
    return is_valid, errors

def validate_config_with_warnings(config: Dict) -> Tuple[bool, List[str], List[str]]:
    """
    带警告的配置验证
    
    Returns:
        (是否有效, 错误列表, 警告列表)
    """
    is_valid, errors = validate_config(config, strict=False)
    warnings = []
    
    # 添加配置建议和警告
    try:
        if 'space' in config:
            space_config = config['space']
            
            # 检查任务数量合理性
            if 'observation' in space_config:
                max_tasks = space_config['observation'].get('max_tasks', 0)
                if max_tasks > 100:
                    warnings.append(f"Large max_tasks ({max_tasks}) may impact performance")
                if max_tasks < 10:
                    warnings.append(f"Small max_tasks ({max_tasks}) may limit task processing")
            
            # 检查奖励权重平衡
            if 'reward' in space_config:
                reward_config = space_config['reward']
                alpha_sum = sum([
                    reward_config.get('alpha_1', 0),
                    reward_config.get('alpha_2', 0), 
                    reward_config.get('alpha_3', 0)
                ])
                penalty_sum = abs(reward_config.get('beta', 0)) + abs(reward_config.get('zeta', 0))
                if alpha_sum < penalty_sum:
                    warnings.append("Penalty weights may be too large compared to reward weights")
    
    except Exception as e:
        warnings.append(f"Warning check failed: {str(e)}")
    
    return is_valid, errors, warnings


def create_task_from_generator(task_generator, location_id: int, timeslot: int):
    """
    直接调用任务生成器生成任务
    
    Args:
        task_generator: TaskGenerator实例
        location_id: 位置ID（0-419）
        timeslot: 时隙
    
    Returns:
        任务对象
    """
    # 直接调用任务生成器
    if task_generator is not None:
        return task_generator.generate_tasks_for_timeslot(timeslot)
    return None


def get_distance_from_orbital(orbital_updater, idx1: int, idx2: int, timeslot: int) -> float:
    """
    直接从OrbitalUpdater获取距离
    
    Args:
        orbital_updater: OrbitalUpdater实例
        idx1: 第一个对象索引
        idx2: 第二个对象索引
        timeslot: 时隙
    
    Returns:
        距离（公里）
    """
    # 直接使用计算好的距离矩阵
    distances = orbital_updater.compute_distances(timeslot)
    if 'satellite_distances' in distances:
        return distances['satellite_distances'][idx1, idx2]
    return 0.0


def get_satellite_region(satellite_idx: int, num_regions: int = 4) -> int:
    """
    获取卫星所属区域
    
    Args:
        satellite_idx: 卫星索引
        num_regions: 区域总数
        
    Returns:
        区域ID
    """
    satellites_per_region = 72 // num_regions
    return min(satellite_idx // satellites_per_region, num_regions - 1)


def create_action_mask_from_mapping(
    action_mapping: Dict[int, Tuple[str, int]],
    max_actions: int = 9
) -> np.ndarray:
    """
    从动作映射创建动作掩码
    
    Args:
        action_mapping: 动作映射字典
        max_actions: 最大动作数
        
    Returns:
        动作掩码数组
    """
    mask = np.zeros(max_actions, dtype=bool)
    for action_idx in action_mapping.keys():
        if action_idx < max_actions:
            mask[action_idx] = True
    return mask


def encode_task_type(task_type: str) -> np.ndarray:
    """
    编码任务类型为one-hot向量
    
    Args:
        task_type: 任务类型字符串
        
    Returns:
        One-hot编码向量
    """
    task_types = ['realtime', 'normal', 'compute_intensive']
    encoding = np.zeros(len(task_types))
    
    if task_type in task_types:
        idx = task_types.index(task_type)
        encoding[idx] = 1.0
    else:
        encoding[1] = 1.0  # 默认为normal
    
    return encoding


def calculate_reward_statistics(
    rewards: Dict[str, float]
) -> Dict[str, float]:
    """
    计算奖励统计信息
    
    Args:
        rewards: 智能体奖励字典
        
    Returns:
        统计信息字典
    """
    reward_values = list(rewards.values())
    
    stats = {
        'mean': np.mean(reward_values),
        'std': np.std(reward_values),
        'min': np.min(reward_values),
        'max': np.max(reward_values),
        'sum': np.sum(reward_values)
    }
    
    return stats