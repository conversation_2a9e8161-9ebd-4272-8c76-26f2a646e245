"""
Detailed test module for satellite compute with timeslot-by-timeslot output
Shows task state transitions and processing progress at each timeslot
"""

import sys
import os
import yaml
import numpy as np
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.env.satellite_cloud.satellite_compute import Satellite<PERSON>ompute, DPSQScheduler
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus
from src.env.physics_layer.task_models import Task, TaskType
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config


class DetailedSatelliteTest:
    """Detailed test class with timeslot-by-timeslot output"""
    
    def __init__(self, config_path: str = "src/env/physics_layer/config.yaml"):
        """Initialize test environment"""
        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize logging
        initialize_logging_and_config(config_path)
        
        # Create satellite instance
        self.satellite = SatelliteCompute(
            satellite_id=1,
            config=self.config,
            enable_gpu=False  # Disable GPU for testing
        )
        
        # Track task history for analysis
        self.task_history = {}
    
    def create_simple_tasks(self) -> List[Task]:
        """Create simple test tasks with varying complexities"""
        tasks = []
        
        # Task 1: Very small (should complete in 1 timeslot)
        tasks.append(Task(
            task_id=1,
            type_id=TaskType.REALTIME,
            data_size_mb=1.0,
            complexity_cycles_per_bit=10,  # Total: 1*8*1e6*10 = 8e7 cycles
            deadline_timestamp=30.0,
            priority=1,  # High priority
            location_id=1,
            coordinates=(0.0, 0.0),
            generation_time=0.0
        ))
        
        # Task 2: Small (should complete in 2-3 timeslots)
        tasks.append(Task(
            task_id=2,
            type_id=TaskType.NORMAL,
            data_size_mb=5.0,
            complexity_cycles_per_bit=50,  # Total: 5*8*1e6*50 = 2e9 cycles
            deadline_timestamp=60.0,
            priority=2,  # Medium priority
            location_id=2,
            coordinates=(10.0, 10.0),
            generation_time=0.0
        ))
        
        # Task 3: Medium (should take 5-10 timeslots)
        tasks.append(Task(
            task_id=3,
            type_id=TaskType.COMPUTE_INTENSIVE,
            data_size_mb=20.0,
            complexity_cycles_per_bit=100,  # Total: 20*8*1e6*100 = 1.6e10 cycles
            deadline_timestamp=100.0,
            priority=3,  # Low priority
            location_id=3,
            coordinates=(20.0, 20.0),
            generation_time=0.0
        ))
        
        return tasks
    
    def print_task_details(self, task: ComputeTask, indent: str = "    "):
        """Print detailed task information"""
        print(f"{indent}Task ID: {task.task_id}")
        print(f"{indent}  Status: {task.status.value}")
        print(f"{indent}  Priority: {task.priority}")
        print(f"{indent}  Data Size: {task.data_size_mb:.1f} MB")
        print(f"{indent}  Total Complexity: {task.complexity:.2e} cycles")
        print(f"{indent}  Remaining Complexity: {task.remaining_complexity:.2e} cycles")
        print(f"{indent}  Progress: {task.processing_progress:.1%}")
        print(f"{indent}  Deadline: {task.deadline:.1f}s")
        
        if hasattr(task, 'allocated_cpu') and task.allocated_cpu:
            print(f"{indent}  CPU Allocation: {task.allocated_cpu:.1%}")
        
        if task.start_time:
            print(f"{indent}  Start Time: {task.start_time:.1f}s")
        
        if task.completion_time:
            print(f"{indent}  Completion Time: {task.completion_time:.1f}s")
            print(f"{indent}  Total Processing Time: {task.accumulated_processing_time:.2f}s")
    
    def record_task_state(self, timeslot: int, task: ComputeTask):
        """Record task state for history tracking"""
        if task.task_id not in self.task_history:
            self.task_history[task.task_id] = []
        
        self.task_history[task.task_id].append({
            'timeslot': timeslot,
            'status': task.status.value,
            'progress': task.processing_progress,
            'remaining_complexity': task.remaining_complexity,
            'accumulated_time': task.accumulated_processing_time
        })
    
    def test_basic_processing(self):
        """Test basic task processing with detailed output"""
        print("\n" + "="*100)
        print("DETAILED SATELLITE PROCESSING TEST")
        print("="*100)
        print(f"Satellite CPU Frequency: {self.satellite.cpu_frequency/1e12:.2f} THz")
        print(f"Timeslot Duration: 3 seconds")
        print(f"Max Parallel Tasks: {self.config['computation'].get('max_parallel_tasks', 200)}")
        print("="*100)
        
        # Reset satellite
        self.satellite.reset()
        self.task_history.clear()
        
        # Create and add tasks
        tasks = self.create_simple_tasks()
        print("\n>>> ADDING TASKS TO QUEUE:")
        for task in tasks:
            success = self.satellite.add_task(task)
            print(f"  Task {task.task_id}: Priority={task.priority}, Size={task.data_size_mb}MB, "
                  f"Complexity={task.complexity_cycles_per_bit*task.data_size_mb*8e6:.2e} cycles - "
                  f"{'[ADDED]' if success else '[REJECTED]'}")
        
        # Initial queue status
        print("\n>>> INITIAL STATE:")
        print(f"  Queue Length: {len(self.satellite.task_queue)}")
        print(f"  Processing Tasks: {len(self.satellite.processing_tasks)}")
        print(f"  Battery: {self.satellite.battery_energy/self.satellite.max_battery:.1%}")
        
        # Process timeslots with detailed output
        timeslot_duration = 3.0  # seconds
        num_timeslots = 20  # Process 20 timeslots (60 seconds)
        
        print("\n>>> TIMESLOT-BY-TIMESLOT PROCESSING:")
        print("-" * 100)
        
        for timeslot in range(num_timeslots):
            # Update current time
            self.satellite.current_time = timeslot * timeslot_duration
            current_time = self.satellite.current_time
            
            print(f"\n[TIMESLOT {timeslot}] Time: {current_time:.1f}s")
            print("  " + "-" * 80)
            
            # Show tasks in queue BEFORE processing
            if self.satellite.task_queue:
                print("  QUEUE (waiting):")
                for task in self.satellite.task_queue:
                    print(f"    - Task {task.task_id}: Priority={task.priority}, "
                          f"Status={task.status.value}, Progress={task.processing_progress:.1%}")
                    self.record_task_state(timeslot, task)
            else:
                print("  QUEUE: Empty")
            
            # Show tasks currently being processed BEFORE this timeslot
            if self.satellite.processing_tasks:
                print("  PROCESSING (before timeslot):")
                for task in self.satellite.processing_tasks:
                    print(f"    - Task {task.task_id}: Priority={task.priority}, "
                          f"Status={task.status.value}, Progress={task.processing_progress:.1%}, "
                          f"Remaining={task.remaining_complexity:.2e} cycles")
                    self.record_task_state(timeslot, task)
            else:
                print("  PROCESSING: No tasks currently processing")
            
            # Process the timeslot
            print(f"\n  >>> Executing timeslot {timeslot} (duration={timeslot_duration}s)...")
            result = self.satellite.process_timeslot(
                duration=timeslot_duration,
                illuminated=True  # Always illuminated for simplicity
            )
            
            # Show what happened during this timeslot
            print(f"\n  TIMESLOT {timeslot} RESULTS:")
            
            # Show tasks being processed AFTER this timeslot
            if self.satellite.processing_tasks:
                print("  Tasks still processing:")
                for task in self.satellite.processing_tasks:
                    progress_change = task.processing_progress - (self.task_history.get(task.task_id, [{}])[-2].get('progress', 0) if len(self.task_history.get(task.task_id, [])) > 1 else 0)
                    print(f"    - Task {task.task_id}: Progress={task.processing_progress:.1%} "
                          f"(+{progress_change:.1%} this timeslot), "
                          f"Remaining={task.remaining_complexity:.2e} cycles")
            
            # Show completed tasks
            if result.completed_tasks:
                print("  Tasks completed this timeslot:")
                for task in result.completed_tasks:
                    print(f"    - Task {task.task_id}: COMPLETED")
                    print(f"      Final progress: {task.processing_progress:.1%}")
                    print(f"      Total time: {task.accumulated_processing_time:.2f}s")
                    print(f"      Energy consumed: {task.accumulated_energy:.2e} J")
            
            # Show resource utilization
            print(f"\n  Resource Status:")
            print(f"    CPU Utilization: {result.cpu_utilization:.1%}")
            print(f"    Energy Consumed: {result.energy_consumed:.2e} J")
            print(f"    Battery Level: {self.satellite.battery_energy/self.satellite.max_battery:.1%}")
            print(f"    Queue Wait Time: {result.queue_wait_time:.2f}s")
            
            # Check if all tasks are done
            if (len(self.satellite.task_queue) == 0 and 
                len(self.satellite.processing_tasks) == 0 and
                len(self.satellite.completed_tasks) == len(tasks)):
                print("\n  >>> ALL TASKS COMPLETED! Stopping simulation.")
                break
        
        print("\n" + "="*100)
        print("FINAL SUMMARY")
        print("="*100)
        
        # Show final statistics
        stats = self.satellite.get_statistics()
        print(f"Total Tasks Processed: {stats['total_tasks_processed']}")
        print(f"Total Tasks Dropped: {stats['total_tasks_dropped']}")
        print(f"Total Energy Consumed: {stats['total_energy_consumed']/1e6:.3f} MJ")
        
        # Show task completion details
        print("\nTask Completion Details:")
        for task in self.satellite.completed_tasks:
            print(f"  Task {task.task_id}:")
            print(f"    Priority: {task.priority}")
            print(f"    Total Complexity: {task.complexity:.2e} cycles")
            print(f"    Processing Time: {task.accumulated_processing_time:.2f}s")
            print(f"    Energy Used: {task.accumulated_energy/1e6:.3f} MJ")
            if task.completion_time and task.arrival_time:
                print(f"    End-to-End Delay: {task.completion_time - task.arrival_time:.2f}s")
        
        # Show task history summary
        print("\nTask State History Summary:")
        for task_id, history in self.task_history.items():
            print(f"  Task {task_id}:")
            # Show key state transitions
            states = [(h['timeslot'], h['status'], h['progress']) for h in history]
            unique_states = []
            last_state = None
            for state in states:
                if last_state is None or state[1] != last_state[1] or abs(state[2] - last_state[2]) > 0.1:
                    unique_states.append(state)
                    last_state = state
            
            for timeslot, status, progress in unique_states[:5]:  # Show first 5 transitions
                print(f"    Timeslot {timeslot}: {status}, Progress={progress:.1%}")
    
    def test_parallel_processing(self):
        """Test parallel processing capabilities"""
        print("\n" + "="*100)
        print("PARALLEL PROCESSING TEST")
        print("="*100)
        
        # Reset satellite
        self.satellite.reset()
        
        # Create multiple small tasks that should process in parallel
        tasks = []
        for i in range(5):
            task = Task(
                task_id=100 + i,
                type_id=TaskType.NORMAL,
                data_size_mb=2.0,
                complexity_cycles_per_bit=20,  # Small complexity
                deadline_timestamp=50.0,
                priority=2,
                location_id=100 + i,
                coordinates=(float(i), float(i)),
                generation_time=0.0
            )
            tasks.append(task)
        
        # Add all tasks
        print(">>> Adding 5 tasks for parallel processing:")
        for task in tasks:
            success = self.satellite.add_task(task)
            print(f"  Task {task.task_id}: Size={task.data_size_mb}MB - {'[ADDED]' if success else '[REJECTED]'}")
        
        # Process one timeslot to see parallel processing
        print("\n>>> Processing single timeslot with parallel tasks:")
        self.satellite.current_time = 0.0
        
        # Show initial state
        print(f"\nBEFORE PROCESSING:")
        print(f"  Queue: {len(self.satellite.task_queue)} tasks")
        print(f"  Processing: {len(self.satellite.processing_tasks)} tasks")
        
        # Process timeslot
        result = self.satellite.process_timeslot(duration=3.0, illuminated=True)
        
        # Show results
        print(f"\nAFTER PROCESSING:")
        print(f"  Queue: {len(self.satellite.task_queue)} tasks")
        print(f"  Processing: {len(self.satellite.processing_tasks)} tasks")
        print(f"  Completed: {len(result.completed_tasks)} tasks")
        
        if self.satellite.processing_tasks:
            print(f"\nTasks being processed in parallel:")
            for task in self.satellite.processing_tasks:
                print(f"  - Task {task.task_id}: Progress={task.processing_progress:.1%}, "
                      f"CPU allocation={1.0/len(self.satellite.processing_tasks):.1%}")
        
        print(f"\nThis demonstrates that {len(self.satellite.processing_tasks)} tasks "
              f"are being processed simultaneously with CPU sharing.")


def main():
    """Main test execution"""
    # Create test instance
    tester = DetailedSatelliteTest()
    
    # Run basic processing test with detailed output
    tester.test_basic_processing()
    
    # Run parallel processing test
    tester.test_parallel_processing()


if __name__ == "__main__":
    main()