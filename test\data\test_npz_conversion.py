"""
NPZ转换测试脚本 - 验证数据转换的正确性
比较原始JSON数据和转换后NPZ数据的一致性
"""

import json
import numpy as np
import time
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.data.npz_data_loader import NPZDataLoader


class ConversionTester:
    def __init__(self, json_file: str = 'integrated_data.json', 
                 npz_dir: str = 'src/data/npz_data'):
        """
        初始化测试器
        
        Args:
            json_file: 原始JSON文件路径
            npz_dir: NPZ数据目录
        """
        self.json_file = json_file
        self.npz_dir = npz_dir
        
        # 检查文件是否存在
        if not os.path.exists(json_file):
            raise FileNotFoundError(f"JSON文件不存在: {json_file}")
        
        if not os.path.exists(npz_dir):
            raise FileNotFoundError(f"NPZ目录不存在: {npz_dir}")
        
        # 创建NPZ加载器
        self.loader = NPZDataLoader(npz_dir)
        
        # 测试结果
        self.test_results = {
            'passed': [],
            'failed': [],
            'warnings': []
        }
    
    def load_json_timeslot(self, timeslot_id: int) -> dict:
        """加载JSON中的指定时隙"""
        print(f"  加载JSON时隙 {timeslot_id}...")
        start = time.time()
        
        with open(self.json_file, 'r', encoding='utf-8') as f:
            # 由于文件很大，使用流式读取
            data = json.load(f)
            if timeslot_id >= len(data):
                raise ValueError(f"时隙 {timeslot_id} 不存在于JSON中")
            
            result = data[timeslot_id]
        
        load_time = time.time() - start
        print(f"    JSON加载时间: {load_time:.2f}秒")
        return result
    
    def test_satellite_states(self, timeslot_id: int = 0):
        """测试卫星状态数据的一致性"""
        print("\n测试1: 卫星状态数据一致性")
        print("-" * 40)
        
        try:
            # 加载数据
            json_data = self.load_json_timeslot(timeslot_id)
            npz_data = self.loader.get_timeslot_as_json(timeslot_id)
            
            # 比较每个卫星的状态
            errors = []
            for sat_id in range(self.loader.num_satellites):
                sat_id_str = str(sat_id)
                
                json_state = json_data['satellites'][sat_id_str]['state']
                npz_state = npz_data['satellites'][sat_id_str]['state']
                
                # 比较状态值
                for i, (j_val, n_val) in enumerate(zip(json_state, npz_state)):
                    if abs(j_val - n_val) > 1e-5:  # 允许小的浮点误差
                        errors.append(f"卫星{sat_id}状态[{i}]: JSON={j_val}, NPZ={n_val}")
            
            if errors:
                self.test_results['failed'].append("卫星状态测试")
                print(f"  ❌ 发现 {len(errors)} 个不一致:")
                for err in errors[:5]:  # 只显示前5个
                    print(f"    - {err}")
            else:
                self.test_results['passed'].append("卫星状态测试")
                print(f"  ✅ 所有卫星状态一致")
        
        except Exception as e:
            self.test_results['failed'].append("卫星状态测试")
            print(f"  ❌ 测试失败: {e}")
    
    def test_ground_links(self, timeslot_id: int = 0):
        """测试地面链路数据的一致性"""
        print("\n测试2: 地面链路数据一致性")
        print("-" * 40)
        
        try:
            json_data = self.load_json_timeslot(timeslot_id)
            npz_data = self.loader.get_timeslot_as_json(timeslot_id)
            
            total_links_json = 0
            total_links_npz = 0
            link_errors = []
            
            for sat_id in range(self.loader.num_satellites):
                sat_id_str = str(sat_id)
                
                json_stations = json_data['satellites'][sat_id_str]['visible_ground_stations']
                npz_stations = npz_data['satellites'][sat_id_str]['visible_ground_stations']
                
                total_links_json += len(json_stations)
                total_links_npz += len(npz_stations)
                
                # 创建映射以便比较
                json_map = {s['station_id']: s for s in json_stations}
                npz_map = {s['station_id']: s for s in npz_stations}
                
                # 检查是否有相同的站点
                if set(json_map.keys()) != set(npz_map.keys()):
                    link_errors.append(f"卫星{sat_id}的可见地面站不一致")
                    continue
                
                # 比较链路参数
                for station_id in json_map:
                    j_link = json_map[station_id]
                    n_link = npz_map[station_id]
                    
                    if abs(j_link['distance_km'] - n_link['distance_km']) > 0.01:
                        link_errors.append(f"卫星{sat_id}-站点{station_id}距离不一致")
                    if abs(j_link['uplink_mbps'] - n_link['uplink_mbps']) > 0.01:
                        link_errors.append(f"卫星{sat_id}-站点{station_id}上行速率不一致")
            
            print(f"  JSON地面链路总数: {total_links_json}")
            print(f"  NPZ地面链路总数: {total_links_npz}")
            
            if link_errors:
                self.test_results['failed'].append("地面链路测试")
                print(f"  ❌ 发现 {len(link_errors)} 个不一致")
                for err in link_errors[:5]:
                    print(f"    - {err}")
            else:
                self.test_results['passed'].append("地面链路测试")
                print(f"  ✅ 地面链路数据一致")
        
        except Exception as e:
            self.test_results['failed'].append("地面链路测试")
            print(f"  ❌ 测试失败: {e}")
    
    def test_tasks(self, timeslot_id: int = 0):
        """测试任务数据的一致性"""
        print("\n测试3: 任务数据一致性")
        print("-" * 40)
        
        try:
            json_data = self.load_json_timeslot(timeslot_id)
            npz_data = self.loader.get_timeslot_as_json(timeslot_id)
            
            json_tasks = []
            npz_tasks = []
            
            # 收集所有任务
            for sat_id_str in json_data['satellites']:
                for station in json_data['satellites'][sat_id_str]['visible_ground_stations']:
                    json_tasks.extend(station.get('tasks', []))
            
            for sat_id_str in npz_data['satellites']:
                for station in npz_data['satellites'][sat_id_str]['visible_ground_stations']:
                    npz_tasks.extend(station.get('tasks', []))
            
            print(f"  JSON任务数: {len(json_tasks)}")
            print(f"  NPZ任务数: {len(npz_tasks)}")
            
            # 按task_id排序后比较
            json_tasks.sort(key=lambda x: x['task_id'])
            npz_tasks.sort(key=lambda x: x['task_id'])
            
            if len(json_tasks) != len(npz_tasks):
                self.test_results['failed'].append("任务数据测试")
                print(f"  ❌ 任务数量不一致")
            else:
                # 比较任务内容
                task_errors = []
                for j_task, n_task in zip(json_tasks, npz_tasks):
                    if j_task['task_id'] != n_task['task_id']:
                        task_errors.append(f"任务ID不匹配")
                    if abs(j_task['data_size_mb'] - n_task['data_size_mb']) > 0.01:
                        task_errors.append(f"任务{j_task['task_id']}数据大小不一致")
                    if abs(j_task['complexity'] - n_task['complexity']) > 0.0001:
                        task_errors.append(f"任务{j_task['task_id']}复杂度不一致")
                
                if task_errors:
                    self.test_results['failed'].append("任务数据测试")
                    print(f"  ❌ 发现 {len(task_errors)} 个不一致")
                    for err in task_errors[:5]:
                        print(f"    - {err}")
                else:
                    self.test_results['passed'].append("任务数据测试")
                    print(f"  ✅ 任务数据一致")
        
        except Exception as e:
            self.test_results['failed'].append("任务数据测试")
            print(f"  ❌ 测试失败: {e}")
    
    def test_isl_links(self, timeslot_id: int = 0):
        """测试ISL链路数据的一致性"""
        print("\n测试4: ISL链路数据一致性")
        print("-" * 40)
        
        try:
            json_data = self.load_json_timeslot(timeslot_id)
            npz_data = self.loader.get_timeslot_as_json(timeslot_id)
            
            total_isl_json = 0
            total_isl_npz = 0
            
            for sat_id in range(self.loader.num_satellites):
                sat_id_str = str(sat_id)
                
                json_isl = json_data['satellites'][sat_id_str]['isl_links']
                npz_isl = npz_data['satellites'][sat_id_str]['isl_links']
                
                total_isl_json += len(json_isl)
                total_isl_npz += len(npz_isl)
            
            print(f"  JSON ISL链路总数: {total_isl_json}")
            print(f"  NPZ ISL链路总数: {total_isl_npz}")
            
            if abs(total_isl_json - total_isl_npz) <= 10:  # 允许小的差异
                self.test_results['passed'].append("ISL链路测试")
                print(f"  ✅ ISL链路数据基本一致")
            else:
                self.test_results['warnings'].append("ISL链路数量有差异")
                print(f"  ⚠️ ISL链路数量有差异")
        
        except Exception as e:
            self.test_results['failed'].append("ISL链路测试")
            print(f"  ❌ 测试失败: {e}")
    
    def test_performance(self):
        """测试性能对比"""
        print("\n测试5: 性能对比")
        print("-" * 40)
        
        try:
            # 测试JSON加载时间
            print("  测试JSON加载性能...")
            json_start = time.time()
            _ = self.load_json_timeslot(0)
            json_time = time.time() - json_start
            
            # 测试NPZ加载时间
            print("  测试NPZ加载性能...")
            self.loader.clear_cache()  # 清空缓存
            npz_start = time.time()
            _ = self.loader.get_timeslot(0)
            npz_time = time.time() - npz_start
            
            # 测试NPZ缓存性能
            cached_start = time.time()
            _ = self.loader.get_timeslot(0)  # 再次加载（使用缓存）
            cached_time = time.time() - cached_start
            
            print(f"\n  性能对比:")
            print(f"    JSON加载时间: {json_time*1000:.2f} ms")
            print(f"    NPZ加载时间: {npz_time*1000:.2f} ms")
            print(f"    NPZ缓存加载: {cached_time*1000:.2f} ms")
            print(f"    加速比: {json_time/npz_time:.1f}x")
            print(f"    缓存加速比: {json_time/cached_time:.1f}x")
            
            self.test_results['passed'].append("性能测试")
            print(f"  ✅ 性能测试完成")
        
        except Exception as e:
            self.test_results['failed'].append("性能测试")
            print(f"  ❌ 测试失败: {e}")
    
    def test_file_sizes(self):
        """测试文件大小对比"""
        print("\n测试6: 文件大小对比")
        print("-" * 40)
        
        try:
            # JSON文件大小
            json_size = os.path.getsize(self.json_file) / (1024 * 1024)
            
            # NPZ文件总大小
            npz_total = 0
            timeslot_dir = Path(self.npz_dir) / 'timeslots'
            for npz_file in timeslot_dir.glob('*.npz'):
                npz_total += os.path.getsize(npz_file)
            npz_size = npz_total / (1024 * 1024)
            
            compression_ratio = (1 - npz_size / json_size) * 100
            
            print(f"  JSON文件大小: {json_size:.1f} MB")
            print(f"  NPZ总大小: {npz_size:.1f} MB")
            print(f"  压缩率: {compression_ratio:.1f}%")
            print(f"  空间节省: {json_size - npz_size:.1f} MB")
            
            self.test_results['passed'].append("文件大小测试")
            print(f"  ✅ 文件大小测试完成")
        
        except Exception as e:
            self.test_results['failed'].append("文件大小测试")
            print(f"  ❌ 测试失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("\n" + "="*60)
        print("NPZ数据转换验证测试")
        print("="*60)
        
        # 运行各项测试
        self.test_satellite_states(timeslot_id=0)
        self.test_ground_links(timeslot_id=0)
        self.test_tasks(timeslot_id=0)
        self.test_isl_links(timeslot_id=0)
        self.test_performance()
        self.test_file_sizes()
        
        # 总结
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        
        print(f"\n✅ 通过: {len(self.test_results['passed'])} 项")
        for test in self.test_results['passed']:
            print(f"  - {test}")
        
        if self.test_results['warnings']:
            print(f"\n⚠️ 警告: {len(self.test_results['warnings'])} 项")
            for warning in self.test_results['warnings']:
                print(f"  - {warning}")
        
        if self.test_results['failed']:
            print(f"\n❌ 失败: {len(self.test_results['failed'])} 项")
            for test in self.test_results['failed']:
                print(f"  - {test}")
        
        # 判断总体结果
        if not self.test_results['failed']:
            print("\n🎉 所有测试通过！数据转换正确。")
            return True
        else:
            print("\n❌ 存在测试失败，请检查转换逻辑。")
            return False


def main():
    """主函数"""
    # 检查是否已经完成转换
    if not os.path.exists('src/data/npz_data'):
        print("错误: NPZ数据目录不存在")
        print("请先运行 python src/data/convert_to_npz.py 进行转换")
        sys.exit(1)
    
    if not os.path.exists('integrated_data.json'):
        print("错误: JSON数据文件不存在")
        print("请先运行 python env_data/integrate_data.py 生成JSON文件")
        sys.exit(1)
    
    # 创建测试器并运行测试
    tester = ConversionTester()
    success = tester.run_all_tests()
    
    # 返回状态码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()