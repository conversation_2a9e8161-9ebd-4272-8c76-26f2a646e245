"""
SPACE2 PettingZoo并行环境主类

实现PettingZoo ParallelEnv接口，提供72个LEO卫星智能体的
多智能体强化学习环境，支持动态动作空间和任务原子性保证
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import gymnasium as gym
from gymnasium import spaces
import pettingzoo
from pettingzoo import ParallelEnv
from pettingzoo.utils import parallel_to_aec, wrappers

# 导入SPACE2现有组件 - 使用绝对导入
from src.env.Foundation_Layer.time_manager import TimeManager
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_distributor import TaskDistributor
from src.env.satellite_cloud.compute_manager import ComputeManager
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.env.metrics_model.load_balancing import LoadBalancingCalculator
from src.env.metrics_model.algorithm_metrics import AlgorithmMetrics

# 导入PettingZoo特定组件
from src.env.space.dynamic_action_space import DynamicActionSpace
from src.env.space.observation_builder import ObservationBuilder
from src.env.space.action_processor import ActionProcessor
from src.env.space.reward_calculator import RewardCalculator


class SpaceParallelEnv(ParallelEnv):
    """
    SPACE2 PettingZoo并行环境
    
    支持72个LEO卫星的协同任务调度，具有动态动作空间和任务原子性保证
    遵循正确的MDP时序：动作基于当前时刻状态执行，返回下一时刻观测
    """
    
    metadata = {
        "render_modes": ["human", "rgb_array"],
        "name": "space2_parallel_env_v0",
        "is_parallelizable": True,
        "render_fps": 1,
    }
    
    def __init__(
        self,
        config_path: str = "src/env/physics_layer/config.yaml",
        space_config_path: str = "src/env/space/space_config.yaml",
        render_mode: Optional[str] = None
    ):
        """
        初始化环境
        
        Args:
            config_path: SPACE2主配置文件路径
            space_config_path: PettingZoo环境专用配置路径
            render_mode: 渲染模式
        """
        super().__init__()
        
        # 加载配置
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        with open(space_config_path, 'r', encoding='utf-8') as f:
            self.space_config = yaml.safe_load(f)
            
        # 设置日志
        import logging
        self.logger = logging.getLogger(__name__)
        
        # 初始化时间管理
        from datetime import datetime
        start_time = datetime(2024, 1, 1, 0, 0, 0)  # 仿真开始时间
        timeslot_duration = 3.0  # 每个时隙3秒（根据config.yaml）
        total_timeslots = 1500  # 总共1500个时隙（根据文档要求）
        self.time_manager = TimeManager(start_time, timeslot_duration, total_timeslots)
        self.current_timeslot = 0
        
        # 初始化物理层组件
        # OrbitalUpdater需要文件路径，不是config字典
        self.orbital_updater = OrbitalUpdater(
            data_file="src/env/env_data/satellite_data_72_0.csv",
            config_file=config_path,
            time_manager=self.time_manager
        )
        # CommunicationManager需要OrbitalUpdater实例
        self.comm_manager = CommunicationManager(
            orbital_updater=self.orbital_updater,
            config_file=config_path,
            time_manager=self.time_manager
        )
        self.task_generator = TaskGenerator(config_path)
        # 加载地理位置数据用于任务生成
        self.task_generator.load_locations_from_csv("src/env/env_data/global_ground_stations.csv")
        # TaskDistributor需要OrbitalUpdater实例
        self.task_distributor = TaskDistributor(
            orbital_updater=self.orbital_updater,
            config_file=config_path,
            time_manager=self.time_manager
        )
        
        # 初始化计算层组件
        self.compute_manager = ComputeManager(config_path)
        
        # 定义智能体
        self.num_satellites = self.config['system']['num_leo_satellites']  # 72
        
        # 初始化卫星计算组件（用于获取真实CPU和队列状态）
        self.satellites_compute = []
        for i in range(self.num_satellites):
            sat_compute = SatelliteCompute(
                satellite_id=str(i),
                config=self.config
            )
            self.satellites_compute.append(sat_compute)
        
        # 初始化负载均衡计算器
        self.load_balancing_calculator = LoadBalancingCalculator(self.config)
        
        # 初始化指标收集系统 - NEW
        self.algorithm_metrics = AlgorithmMetrics(config_path)
        
        # 加载区域数据
        import json
        with open('src/env/env_data/regions.json', 'r') as f:
            self.regions = json.load(f)
        
        # 初始化PettingZoo特定组件
        self.dynamic_action_space = DynamicActionSpace(self.space_config)
        self.observation_builder = ObservationBuilder(
            self.config, 
            self.space_config,
            self.comm_manager,
            self.load_balancing_calculator,
            self.satellites_compute
        )
        self.action_processor = ActionProcessor(self.config, self.space_config)
        self.reward_calculator = RewardCalculator(
            self.config, 
            self.space_config,
            self.load_balancing_calculator,
            self.regions
        )
        
        # 已经定义了num_satellites
        self.agents = [f"satellite_{i}" for i in range(self.num_satellites)]
        self.possible_agents = self.agents[:]
        
        # 关键状态缓存 - 保存当前时刻t的状态供动作执行使用
        self._current_visibility_matrices = None
        self._current_distances = None
        self._current_action_mappings = {}
        self._current_action_masks = {}
        self._current_satellites = None
        self._current_link_qualities = None
        self._current_regional_loads = None  # 缓存区域负载数据
        
        # 初始化观测和动作空间
        self._init_spaces()
        
        # 渲染相关
        self.render_mode = render_mode
        
        self.logger.info("SpaceParallelEnv initialized with 72 satellites")
    
    def _init_spaces(self):
        """初始化观测和动作空间"""
        # 观测空间定义（符合文档要求）
        obs_space = spaces.Dict({
            "self_state": spaces.Box(low=0, high=1, shape=(11,), dtype=np.float32),
            "neighbor_states": spaces.Box(low=0, high=1, shape=(6, 6), dtype=np.float32),  # 6个邻居
            "cloud_state": spaces.Box(low=0, high=1, shape=(5,), dtype=np.float32),  # 1个云中心
            "task_sequence": spaces.Box(low=0, high=1, shape=(35, 9), dtype=np.float32),
            "communication_links": spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32),  # 到6个邻居的链路质量
            "regional_load": spaces.Box(low=0, high=1, shape=(4,), dtype=np.float32),
            "action_mapping": spaces.Box(low=-1, high=100, shape=(9, 2), dtype=np.int32),  # 9个动作
            "neighbor_mask": spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32),  # 6个邻居掩码
            "task_mask": spaces.Box(low=0, high=1, shape=(35,), dtype=np.float32),
            "action_mask": spaces.Box(low=0, high=1, shape=(9,), dtype=np.float32),  # 9个动作掩码
        })
        
        # 动作空间定义 - 9个离散动作选项
        act_space = spaces.Dict({
            "offloading_decisions": spaces.MultiDiscrete([9] * 35),  # 每个任务9个选择
            "resource_allocation": spaces.Box(low=0, high=1, shape=(35,), dtype=np.float32),
        })
        
        # 为每个智能体分配空间
        self.observation_spaces = {agent: obs_space for agent in self.agents}
        self.action_spaces = {agent: act_space for agent in self.agents}
    
    def reset(self, seed: Optional[int] = None, options: Optional[dict] = None):
        """
        重置环境到初始状态t=0
        
        Returns:
            observations: 所有智能体的初始观测
            infos: 额外信息
        """
        if seed is not None:
            np.random.seed(seed)
        
        # 重置时间
        self.current_timeslot = 0
        # TimeManager不需要reset，只需要重置当前时隙
        
        # 重置所有组件
        self.compute_manager.reset()
        self.task_generator.reset()
        self.task_distributor.reset()
        
        # 获取t=0的物理状态
        self._update_current_state(0)
        
        # 生成t=0的观测（包含t=0的动作空间信息）
        observations = self._generate_observations()
        infos = {agent: {} for agent in self.agents}
        
        self.logger.info("Environment reset to timeslot 0")
        
        return observations, infos
    
    def step(self, actions: Dict[str, Dict]):
        """
        执行一步环境更新
        
        正确的MDP时序：
        1. 基于t时刻缓存状态执行动作（动作映射一致性保证）
        2. 推进时间到t+1，更新物理状态  
        3. 基于t时刻动作在新状态下的结果计算奖励
        4. 生成t+1时刻的观测和动作空间（为下次决策准备）
        
        Args:
            actions: 每个智能体在t时刻的动作字典
            
        Returns:
            observations: t+1时刻的观测（下次决策用）
            rewards: t时刻动作的奖励
            terminations: 终止标志
            truncations: 截断标志
            infos: 额外信息
        """
        # 保存t时刻的动作映射（用于奖励计算）
        previous_action_mappings = self._current_action_mappings.copy()
        
        # Phase 1: 在时刻t执行动作（使用t时刻缓存的状态）
        # 重要：此时使用的动作映射与智能体观测到的一致
        self._execute_actions_at_current_time(actions)
        
        # Phase 2: 状态转移 - 推进时间到t+1
        self.current_timeslot += 1
        time_context = self.time_manager.get_time_context(self.current_timeslot)
        
        # t+1时刻的物理状态将在_update_current_state中获取
        # orbital数据是静态的，不需要更新位置
        
        # 生成和分发t+1时刻的新任务
        tasks_by_location = self.task_generator.generate_tasks_for_timeslot(self.current_timeslot)
        # 将字典格式的任务转换为列表格式
        new_tasks = []
        for location_id, tasks in tasks_by_location.items():
            new_tasks.extend(tasks)
        self.task_distributor.distribute_tasks(new_tasks, self.current_timeslot)
        
        # Phase 3: 执行计算处理并结算（处理t时刻动作的结果）
        # 获取时隙持续时间和光照状态
        timeslot_duration = self.config['system']['timeslot_duration_s']  # 3秒
        
        # 获取卫星光照状态（从当前卫星数据中获取）
        illuminated_status = {}
        for i in range(self.num_satellites):
            sat = self._get_satellite_by_idx(i)
            if sat and hasattr(sat, 'light'):
                illuminated_status[i] = sat.light
            else:
                illuminated_status[i] = True  # 默认有光照
        
        computation_results = self.compute_manager.process_timeslot(
            self.current_timeslot, 
            timeslot_duration, 
            illuminated_status
        )
        
        # Collect comprehensive metrics using AlgorithmMetrics - NEW
        simulation_data = {
            'satellites': self.satellites_compute,
            'satellites_tasks': [sat.completed_tasks for sat in self.satellites_compute],
            'task_records': []  # TaskTrackingRecord will be added later if needed
        }
        
        # Extract unified metrics
        unified_metrics = self.algorithm_metrics.extract_metrics(
            simulation_data,
            time_window=(self.current_timeslot * timeslot_duration, 
                        (self.current_timeslot + 1) * timeslot_duration)
        )
        
        # Merge unified metrics with computation results
        computation_results['unified_metrics'] = unified_metrics
        
        # Phase 4: 计算奖励（基于t时刻动作在t+1状态下的结果）
        rewards = {}
        for agent_id in self.agents:
            rewards[agent_id] = self.reward_calculator.calculate(
                agent_id, 
                computation_results,
                previous_action_mappings.get(agent_id, {})  # 使用t时刻的动作映射
            )
        
        # Phase 5: 准备t+1时刻的状态（为下次决策准备）
        self._update_current_state(self.current_timeslot)
        
        # 生成t+1时刻的观测（包含t+1的动作空间）
        observations = self._generate_observations()
        
        # 检查终止条件
        terminations = {agent: False for agent in self.agents}
        truncations = {agent: False for agent in self.agents}
        
        # 使用环境配置的最大步数（1500）
        max_steps = self.space_config['space']['environment']['max_episode_steps']
        if self.current_timeslot >= max_steps:
            truncations = {agent: True for agent in self.agents}
        
        infos = {agent: {"timeslot": self.current_timeslot} for agent in self.agents}
        
        return observations, rewards, terminations, truncations, infos
    
    def _get_satellite_by_idx(self, satellite_idx: int):
        """
        根据索引获取卫星对象
        
        Args:
            satellite_idx: 卫星索引
            
        Returns:
            卫星对象或None
        """
        if self._current_satellites:
            sat_key = str(satellite_idx)
            return self._current_satellites.get(sat_key, None)
        return None
    
    def _update_current_state(self, timeslot: int):
        """
        更新并缓存当前时刻的状态
        
        Args:
            timeslot: 时隙编号
        """
        # 获取卫星位置和状态
        self._current_satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        
        # 计算可见性矩阵 - 分别构建三种可见性矩阵
        sat_vis, sat_distances = self.orbital_updater.build_visibility_matrix(self._current_satellites)
        sat_ground_vis, sat_ground_distances = self.orbital_updater.build_satellite_ground_visibility_matrix(
            self._current_satellites, self.orbital_updater.ground_stations
        )
        sat_cloud_vis, sat_cloud_distances = self.orbital_updater.build_satellite_cloud_visibility_matrix(
            self._current_satellites, self.orbital_updater.cloud_stations
        )
        
        self._current_visibility_matrices = {
            'satellite_to_satellite': sat_vis,
            'satellite_to_ground': sat_ground_vis,
            'satellite_to_cloud': sat_cloud_vis
        }
        
        # 使用已计算的距离矩阵
        self._current_distances = {
            'satellite_distances': sat_distances,
            'satellite_ground_distances': sat_ground_distances,
            'satellite_cloud_distances': sat_cloud_distances
        }
        
        # 计算链路质量 - 使用通信管理器的实际方法
        isl_comm = self.comm_manager.get_isl_communication_matrix(timeslot)
        sat_ground_comm = self.comm_manager.get_satellite_ground_communication_matrix(timeslot)
        sat_cloud_comm = self.comm_manager.get_satellite_cloud_communication_matrix(timeslot)
        
        # 构建链路质量字典，使用SNR作为链路质量指标
        self._current_link_qualities = {
            'satellite_to_satellite': isl_comm.get('snr_matrix', np.ones((72, 72)) * 0.5),
            'satellite_to_ground': sat_ground_comm.get('snr_matrix', np.ones((72, 420)) * 0.5),
            'satellite_to_cloud': sat_cloud_comm.get('snr_matrix', np.ones((72, 5)) * 0.5)
        }
        
        # 直接调用简单的区域负载计算方法
        self._current_regional_loads = self.load_balancing_calculator.get_simple_regional_loads(
            self.satellites_compute, timeslot
        )
        
        # 更新每个卫星的动作空间并缓存
        for agent_id in self.agents:
            satellite_idx = int(agent_id.split('_')[1])
            mapping, mask = self.dynamic_action_space.compute_action_space(
                satellite_idx,
                self._current_visibility_matrices,
                self._current_distances
            )
            self._current_action_mappings[agent_id] = mapping
            self._current_action_masks[agent_id] = mask
    
    def _execute_actions_at_current_time(self, actions: Dict):
        """
        在当前时刻t执行动作
        使用缓存的t时刻动作映射验证和执行
        
        Args:
            actions: 动作字典
        """
        for agent_id, action in actions.items():
            satellite_idx = int(agent_id.split('_')[1])
            
            # 使用当前时刻的动作映射
            action_mapping = self._current_action_mappings.get(agent_id, {})
            
            # 执行动作处理
            satellite_compute = self.satellites_compute[satellite_idx] if satellite_idx < len(self.satellites_compute) else None
            self.action_processor.process(
                satellite_idx,
                action,
                action_mapping,
                self.compute_manager,
                self.current_timeslot,
                satellite_compute
            )
    
    def _generate_observations(self) -> Dict:
        """
        生成所有智能体的观测
        包含当前缓存的动作空间信息
        
        Returns:
            所有智能体的观测字典
        """
        observations = {}
        
        for agent_id in self.agents:
            satellite_idx = int(agent_id.split('_')[1])
            
            # 直接从卫星计算组件获取任务队列
            task_queue = self.satellites_compute[satellite_idx].task_queue if satellite_idx < len(self.satellites_compute) else []
            
            # 构建观测
            observations[agent_id] = self.observation_builder.build(
                agent_id,
                self._current_satellites,
                self._current_visibility_matrices,
                self._current_link_qualities,
                self._current_action_mappings.get(agent_id, {}),
                self._current_action_masks.get(agent_id, np.zeros(9)),  # 9维动作掩码
                task_queue,
                self._current_regional_loads
            )
        
        return observations
    
    def render(self):
        """渲染环境（可选实现）"""
        if self.render_mode == "human":
            print(f"Timeslot: {self.current_timeslot}")
            # 可以添加更多可视化信息
    
    def close(self):
        """清理资源"""
        self.logger.info("Environment closed")
        # 清理任何需要关闭的资源
    
    def state(self) -> np.ndarray:
        """返回全局状态（用于集中式训练）"""
        # 可选实现，返回全局状态向量
        pass
    
    def observation_space(self, agent: str) -> gym.Space:
        """返回指定智能体的观测空间"""
        return self.observation_spaces[agent]
    
    def action_space(self, agent: str) -> gym.Space:
        """返回指定智能体的动作空间"""
        return self.action_spaces[agent]