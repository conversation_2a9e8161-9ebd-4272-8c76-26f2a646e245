"""
观测空间构建模块

为每个卫星智能体构建包含动态动作空间信息的观测
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any


class ObservationBuilder:
    """
    观测构建器
    
    构建包含自身状态、邻居状态、任务信息和动作空间的完整观测
    """
    
    def __init__(self, config: Dict, space_config: Dict, 
                 comm_manager=None, load_balancing_calculator=None, 
                 satellites_compute=None):
        """
        初始化观测构建器
        
        Args:
            config: SPACE2主配置
            space_config: PettingZoo环境配置
            comm_manager: 通信管理器（用于获取真实链路数据）
            load_balancing_calculator: 负载均衡计算器
            satellites_compute: 卫星计算组件列表
        """
        self.config = config
        self.space_config = space_config['space']
        self.comm_manager = comm_manager
        self.load_balancing_calculator = load_balancing_calculator
        self.satellites_compute = satellites_compute
        
        # 观测空间参数
        self.max_neighbors = self.space_config['observation']['max_neighbors']  # 6
        self.max_clouds = self.space_config['observation']['max_clouds']  # 1
        self.max_tasks = self.space_config['observation']['max_tasks']  # 35
        self.max_actions = self.space_config['action']['max_actions']  # 9
        
        # 维度参数
        self.self_state_dim = self.space_config['observation']['self_state_dim']  # 11
        self.neighbor_state_dim = self.space_config['observation']['neighbor_state_dim']  # 6
        self.cloud_state_dim = self.space_config['observation']['cloud_state_dim']  # 5
        self.task_feature_dim = self.space_config['observation']['task_feature_dim']  # 9
    
    def _get_satellite_by_index(self, satellites, satellite_idx: int):
        """
        统一的卫星数据访问接口
        
        Args:
            satellites: 卫星数据（字典或列表格式）
            satellite_idx: 卫星索引
            
        Returns:
            卫星对象或None
        """
        if satellites is None:
            return None
            
        if isinstance(satellites, dict):
            sat_key = f'sat_{satellite_idx:02d}'
            return satellites.get(sat_key, None)
        elif isinstance(satellites, list):
            if 0 <= satellite_idx < len(satellites):
                return satellites[satellite_idx]
            return None
        else:
            # 未知格式，尝试直接索引
            try:
                return satellites[satellite_idx]
            except (KeyError, IndexError, TypeError):
                return None
    
    def _get_all_satellites_list(self, satellites) -> List:
        """
        将卫星数据统一转换为列表格式
        
        Args:
            satellites: 卫星数据（任意格式）
            
        Returns:
            卫星对象列表
        """
        if satellites is None:
            return []
            
        if isinstance(satellites, dict):
            # 按索引顺序返回卫星列表
            result = []
            for i in range(72):  # 假设最多72个卫星
                sat_key = f'sat_{i:02d}'
                if sat_key in satellites:
                    result.append(satellites[sat_key])
                else:
                    result.append(None)  # 占位符
            return result
        elif isinstance(satellites, list):
            return list(satellites)
        else:
            # 尝试转换为列表
            try:
                return list(satellites)
            except TypeError:
                return []
        
    def build(
        self,
        agent_id: str,
        satellites: List,
        visibility_matrices: Dict[str, np.ndarray],
        link_qualities: Dict[str, np.ndarray],
        action_mapping: Dict[int, Tuple[str, int]],
        action_mask: np.ndarray,
        task_queue: List,
        regional_loads: Dict = None
    ) -> Dict[str, np.ndarray]:
        """
        构建单个智能体的观测
        
        Args:
            agent_id: 智能体ID
            satellites: 卫星列表
            visibility_matrices: 可见性矩阵
            link_qualities: 链路质量矩阵
            action_mapping: 动作映射
            action_mask: 动作掩码
            task_queue: 任务队列
            
        Returns:
            观测字典
        """
        satellite_idx = int(agent_id.split('_')[1])
        
        # 构建各个观测组件
        self_state = self._build_self_state(satellite_idx, satellites, task_queue)
        
        neighbor_states, neighbor_mask = self._build_neighbor_states(
            satellite_idx, action_mapping, satellites, link_qualities
        )
        
        cloud_state = self._build_cloud_state(
            satellite_idx, action_mapping, link_qualities
        )
        
        task_sequence, task_mask = self._build_task_sequence(task_queue)
        
        communication_links = self._build_communication_links(
            satellite_idx, visibility_matrices, link_qualities, action_mapping
        )
        
        regional_load = self._build_regional_load(satellites, satellite_idx, regional_loads)
        
        action_mapping_array = self._encode_action_mapping(action_mapping)
        
        return {
            'self_state': self_state.astype(np.float32),
            'neighbor_states': neighbor_states.astype(np.float32),
            'cloud_state': cloud_state.astype(np.float32),  # 注意：没有s，只有一个云中心
            'task_sequence': task_sequence.astype(np.float32),
            'communication_links': communication_links.astype(np.float32),
            'regional_load': regional_load.astype(np.float32),
            'action_mapping': action_mapping_array.astype(np.int32),
            'neighbor_mask': neighbor_mask.astype(np.float32),
            'task_mask': task_mask.astype(np.float32),
            'action_mask': action_mask.astype(np.float32)
        }
    
    def _build_self_state(
        self,
        satellite_idx: int,
        satellites: List,
        task_queue: List
    ) -> np.ndarray:
        """
        构建自身状态向量
        
        Returns:
            11维状态向量
        """
        state = np.zeros(self.self_state_dim)
        
        # 使用统一接口获取卫星数据
        sat = self._get_satellite_by_index(satellites, satellite_idx)
        if sat is None:
            return state
            
        # 位置信息 (3维) - 归一化的经纬度和高度
        state[0] = (sat.latitude + 90) / 180  # 纬度归一化到[0,1]
        state[1] = (sat.longitude + 180) / 360  # 经度归一化到[0,1]
        state[2] = sat.altitude / 1000000 if hasattr(sat, 'altitude') else 0.5  # 高度归一化
        
        # 能量信息 (1维)
        state[3] = sat.energy_level if hasattr(sat, 'energy_level') else 0.8
        
        # CPU利用率 (1维)
        state[4] = sat.cpu_utilization if hasattr(sat, 'cpu_utilization') else 0.3
        
        # 队列长度 (1维)
        state[5] = min(len(task_queue) / self.max_tasks, 1.0)
        
        # 各优先级任务数 (3维)
        priority_counts = [0, 0, 0]
        for task in task_queue[:self.max_tasks]:
            if hasattr(task, 'priority'):
                p = min(max(task.priority - 1, 0), 2)
                priority_counts[p] += 1
        
        state[6:9] = np.array(priority_counts) / max(len(task_queue), 1)
        
        # 平均等待时间 (1维)
        if task_queue:
            avg_wait = np.mean([getattr(task, 'waiting_time', 0) for task in task_queue[:10]])
            state[9] = min(avg_wait / 100, 1.0)  # 归一化到[0,1]
        
        # 时间信息 (1维) - 当前时隙归一化
        state[10] = 0.0  # 将在环境中更新
        
        return state
    
    def _build_neighbor_states(
        self,
        satellite_idx: int,
        action_mapping: Dict,
        satellites: List,
        link_qualities: Dict
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        构建邻居状态矩阵
        严格按照action_mapping[1-6]的顺序排列
        
        Returns:
            neighbor_states: (6, 6) 邻居状态矩阵
            neighbor_mask: (6,) 有效性掩码
        """
        neighbor_states = np.zeros((self.max_neighbors, self.neighbor_state_dim))
        neighbor_mask = np.zeros(self.max_neighbors)
        
        # 按照动作映射的顺序填充邻居状态（动作1-6对应邻居）
        for action_idx in range(1, self.max_neighbors + 1):  # 1到6
            if action_idx in action_mapping:
                target_type, neighbor_idx = action_mapping[action_idx]
                if target_type == 'satellite':
                    # 使用统一接口获取邻居卫星数据
                    neighbor = self._get_satellite_by_index(satellites, neighbor_idx)
                    if neighbor is None:
                        continue
                        
                    state_idx = action_idx - 1  # 映射到状态数组索引（0-5）
                    
                    # 相对位置 (3维)
                    my_sat = self._get_satellite_by_index(satellites, satellite_idx)
                    
                    if my_sat:
                        neighbor_states[state_idx, 0] = (neighbor.latitude - my_sat.latitude) / 180
                        neighbor_states[state_idx, 1] = (neighbor.longitude - my_sat.longitude) / 360
                        neighbor_states[state_idx, 2] = 0.5  # 相对高度占位
                    
                    # 链路质量 (1维)
                    if link_qualities is not None and 'satellite_to_satellite' in link_qualities:
                        quality = link_qualities['satellite_to_satellite'][satellite_idx, neighbor_idx]
                        neighbor_states[state_idx, 3] = quality
                    else:
                        neighbor_states[state_idx, 3] = 0.5
                    
                    # CPU利用率 (1维)
                    neighbor_states[state_idx, 4] = getattr(neighbor, 'cpu_utilization', 0.3)
                    
                    # 队列长度 (1维)
                    neighbor_states[state_idx, 5] = getattr(neighbor, 'queue_length', 0.2)
                    
                    neighbor_mask[state_idx] = 1.0
        
        return neighbor_states, neighbor_mask
    
    def _build_cloud_state(
        self,
        satellite_idx: int,
        action_mapping: Dict,
        link_qualities: Dict
    ) -> np.ndarray:
        """
        构建云中心状态向量（只有一个云中心）
        
        Returns:
            cloud_state: (5,) 云中心状态向量
        """
        cloud_state = np.zeros(self.cloud_state_dim)
        
        # 云中心对应动作7
        if 7 in action_mapping:
            target_type, cloud_idx = action_mapping[7]
            if target_type == 'cloud':
                # 相对位置 (3维) - 使用占位值
                cloud_state[0:3] = [0.5, 0.5, 0.0]
                
                # 链路质量 (1维)
                if link_qualities is not None and 'satellite_to_cloud' in link_qualities:
                    quality = link_qualities['satellite_to_cloud'][satellite_idx, cloud_idx]
                    cloud_state[3] = quality
                else:
                    cloud_state[3] = 0.8
                
                # CPU利用率 (1维) - 云中心通常负载较低
                cloud_state[4] = 0.2
        
        return cloud_state
    
    def _build_task_sequence(self, task_queue: List) -> Tuple[np.ndarray, np.ndarray]:
        """
        构建任务序列
        
        Returns:
            task_sequence: (100, 9) 任务特征矩阵
            task_mask: (100,) 有效任务掩码
        """
        task_sequence = np.zeros((self.max_tasks, self.task_feature_dim))
        task_mask = np.zeros(self.max_tasks)
        
        for i, task in enumerate(task_queue[:self.max_tasks]):
            # 数据大小 (1维)
            task_sequence[i, 0] = min(getattr(task, 'data_size_mb', 10) / 100, 1.0)
            
            # 计算复杂度 (1维)
            task_sequence[i, 1] = min(getattr(task, 'complexity', 1000) / 10000, 1.0)
            
            # 优先级 (1维)
            task_sequence[i, 2] = getattr(task, 'priority', 2) / 3
            
            # 已等待时间 (1维)
            task_sequence[i, 3] = min(getattr(task, 'waiting_time', 0) / 100, 1.0)
            
            # 剩余截止时间 (1维)
            task_sequence[i, 4] = min(getattr(task, 'remaining_deadline', 100) / 200, 1.0)
            
            # 来源用户ID (1维)
            task_sequence[i, 5] = getattr(task, 'source_user_id', 0) / 420
            
            # 处理进度 (1维)
            task_sequence[i, 6] = getattr(task, 'progress', 0.0)
            
            # 任务类型 (2维) - one-hot编码
            task_type = getattr(task, 'task_type', 0)
            if task_type < 2:
                task_sequence[i, 7 + task_type] = 1.0
            
            task_mask[i] = 1.0
        
        return task_sequence, task_mask
    
    def _build_communication_links(
        self,
        satellite_idx: int,
        visibility_matrices: Dict,
        link_qualities: Dict,
        action_mapping: Dict
    ) -> np.ndarray:
        """
        获取当前卫星到邻居的通信链路质量
        
        Returns:
            (6,) 当前卫星到每个邻居的链路质量向量
        """
        comm_links = np.zeros(self.max_neighbors)
        
        # 从action_mapping获取6个邻居，按顺序填充链路质量
        for action_idx in range(1, 7):  # 动作1-6对应邻居
            if action_idx in action_mapping:
                target_type, neighbor_idx = action_mapping[action_idx]
                if target_type == 'satellite':
                    # 从链路质量矩阵中提取当前卫星到该邻居的链路质量
                    if link_qualities is not None and 'satellite_to_satellite' in link_qualities:
                        full_link_matrix = link_qualities['satellite_to_satellite']
                        if satellite_idx < full_link_matrix.shape[0] and neighbor_idx < full_link_matrix.shape[1]:
                            # action_idx-1 映射到数组索引0-5
                            comm_links[action_idx - 1] = full_link_matrix[satellite_idx, neighbor_idx]
                    else:
                        # 没有链路数据时使用默认值
                        comm_links[action_idx - 1] = 0.5
        
        return comm_links
    
    def _build_regional_load(self, satellites: List, satellite_idx: int, 
                            regional_loads: Dict = None) -> np.ndarray:
        """
        构建区域负载信息（使用真实数据）
        
        Returns:
            4维区域负载向量
        """
        regional_load = np.zeros(4)
        
        if regional_loads is not None:
            # 获取当前卫星所属区域
            sat = self._get_satellite_by_index(satellites, satellite_idx)
            
            if sat and hasattr(sat, 'latitude') and hasattr(sat, 'longitude'):
                region_id = self._get_satellite_region(sat.latitude, sat.longitude)
                
                if region_id in regional_loads:
                    region_data = regional_loads[region_id]
                    regional_load[0] = region_data.get('mean_cpu_utilization', 0.0)
                    regional_load[1] = region_data.get('mean_queue_length', 0.0) / 100  # 归一化
                    regional_load[2] = region_data.get('load_variance', 0.0)
                    regional_load[3] = region_data.get('total_tasks', 0.0) / 1000  # 归一化
        elif self.satellites_compute is not None:
            # 备用：直接从卫星计算组件获取
            cpu_utils = []
            queue_lens = []
            for sat_compute in self.satellites_compute:
                cpu_utils.append(sat_compute.get_cpu_utilization())
                queue_lens.append(len(sat_compute.task_queue))
            
            if cpu_utils:
                regional_load[0] = np.mean(cpu_utils)
                regional_load[1] = np.mean(queue_lens) / 100
                regional_load[2] = np.var(cpu_utils)
                regional_load[3] = sum(queue_lens) / 1000
        
        return regional_load
    
    def _get_satellite_region(self, latitude: float, longitude: float) -> int:
        """
        根据经纬度获取卫星所属区域（基于regions.json）
        """
        # 读取regions.json进行精确匹配
        try:
            import json
            with open('src/env/env_data/regions.json', 'r') as f:
                regions = json.load(f)
                
            for region in regions:
                lat_range = region['latitude_range']
                lon_range = region['longitude_range']
                
                if (lat_range['min'] <= latitude <= lat_range['max'] and
                    lon_range['min'] <= longitude <= lon_range['max']):
                    return region['region_id']
        except:
            pass
        
        # 备用方案：基于简单划分
        # 4个纬度带×6个经度带=24个区域
        lat_band = 0
        if latitude >= 30:
            lat_band = 0
        elif latitude >= 0:
            lat_band = 1  
        elif latitude >= -30:
            lat_band = 2
        else:
            lat_band = 3
            
        lon_band = int((longitude + 180) // 60)
        lon_band = min(max(lon_band, 0), 5)
        
        return lat_band * 6 + lon_band + 1
    
    def _encode_action_mapping(self, action_mapping: Dict) -> np.ndarray:
        """
        编码动作映射为数组形式
        
        Returns:
            (9, 2) 动作映射数组
        """
        encoded = np.full((self.max_actions, 2), -1, dtype=np.int32)
        
        for action_idx, (target_type, target_idx) in action_mapping.items():
            if action_idx < self.max_actions:
                # 编码目标类型: 0=local, 1=satellite, 2=cloud
                if target_type == 'local':
                    encoded[action_idx, 0] = 0
                elif target_type == 'satellite':
                    encoded[action_idx, 0] = 1
                elif target_type == 'cloud':
                    encoded[action_idx, 0] = 2
                
                # 编码目标索引
                encoded[action_idx, 1] = target_idx if target_idx is not None else -1
        
        return encoded