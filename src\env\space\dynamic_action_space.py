"""
动态动作空间管理模块

根据实时可见性计算每个卫星的可用动作目标
最多支持9个动作选项：1个本地+6个邻居+1个云中心+1个丢弃
"""

import numpy as np
from typing import Dict, Tuple, List


class DynamicActionSpace:
    """
    动态动作空间管理器
    
    基于可见性和距离动态确定每个卫星的可用卸载目标
    """
    
    def __init__(self, space_config: Dict):
        """
        初始化动态动作空间管理器
        
        Args:
            space_config: Petting<PERSON>oo环境配置
        """
        self.config = space_config['space']
        
        # 动作空间参数
        self.max_actions = self.config['action']['max_actions']  # 9
        self.max_neighbors = self.config['action']['num_neighbors']  # 6
        self.max_clouds = self.config['action']['num_clouds']  # 1
        
        # 距离阈值
        self.sat_distance_threshold = self.config['dynamic_action']['satellite_distance_threshold_km']
        self.cloud_distance_threshold = self.config['dynamic_action']['cloud_distance_threshold_km']
        
    def compute_action_space(
        self,
        satellite_idx: int,
        visibility_matrices: Dict[str, np.ndarray],
        distances: Dict[str, np.ndarray]
    ) -> Tuple[Dict[int, Tuple[str, int]], np.ndarray]:
        """
        计算单个卫星的动态动作空间
        
        Args:
            satellite_idx: 卫星索引
            visibility_matrices: 可见性矩阵字典
                - 'satellite_to_satellite': (72, 72)
                - 'satellite_to_cloud': (72, 5)
            distances: 距离矩阵字典
                - 'satellite_distances': (72, 72)
                - 'satellite_cloud_distances': (72, 5)
                
        Returns:
            action_mapping: 动作索引到目标的映射 {action_idx: (target_type, target_idx)}
            action_mask: 有效动作掩码 (9,)
        """
        action_mapping = {}
        action_mask = np.zeros(self.max_actions, dtype=bool)
        
        # 动作0: 本地处理（始终可用）
        action_mapping[0] = ('local', satellite_idx)
        action_mask[0] = True
        
        # 动作1-6: 邻居卫星
        neighbor_indices, neighbor_count = self._select_neighbor_satellites(
            satellite_idx,
            visibility_matrices['satellite_to_satellite'],
            distances['satellite_distances']
        )
        
        for i, neighbor_idx in enumerate(neighbor_indices):
            if i < self.max_neighbors:
                action_mapping[i + 1] = ('satellite', neighbor_idx)
                action_mask[i + 1] = True
        
        # 动作7: 云中心（只有1个）
        cloud_indices, cloud_count = self._select_cloud_centers(
            satellite_idx,
            visibility_matrices['satellite_to_cloud'],
            distances['satellite_cloud_distances']
        )
        
        if cloud_indices and len(cloud_indices) > 0:
            action_mapping[7] = ('cloud', cloud_indices[0])
            action_mask[7] = True
        
        # 动作8: 丢弃任务（始终可用）
        action_mapping[8] = ('drop', None)
        action_mask[8] = True
        
        return action_mapping, action_mask
    
    def _select_neighbor_satellites(
        self,
        satellite_idx: int,
        sat_visibility: np.ndarray,
        sat_distances: np.ndarray
    ) -> Tuple[List[int], int]:
        """
        选择最近的可见邻居卫星
        
        Args:
            satellite_idx: 当前卫星索引
            sat_visibility: 卫星间可见性矩阵 (72, 72)
            sat_distances: 卫星间距离矩阵 (72, 72)
            
        Returns:
            selected_neighbors: 选中的邻居卫星索引列表
            count: 实际可见邻居数量
        """
        # 获取可见卫星（排除自己）
        visible_satellites = np.where(sat_visibility[satellite_idx])[0]
        visible_satellites = visible_satellites[visible_satellites != satellite_idx]
        
        if len(visible_satellites) == 0:
            return [], 0
        
        # 获取到可见卫星的距离
        distances_to_visible = sat_distances[satellite_idx, visible_satellites]
        
        # 应用距离阈值
        within_threshold = distances_to_visible <= self.sat_distance_threshold
        valid_satellites = visible_satellites[within_threshold]
        valid_distances = distances_to_visible[within_threshold]
        
        if len(valid_satellites) == 0:
            return [], 0
        
        # 按距离排序，选择最近的N个
        sorted_indices = np.argsort(valid_distances)
        selected_count = min(len(valid_satellites), self.max_neighbors)
        selected_neighbors = valid_satellites[sorted_indices[:selected_count]].tolist()
        
        return selected_neighbors, len(valid_satellites)
    
    def _select_cloud_centers(
        self,
        satellite_idx: int,
        cloud_visibility: np.ndarray,
        cloud_distances: np.ndarray
    ) -> Tuple[List[int], int]:
        """
        选择最近的可见云中心
        
        Args:
            satellite_idx: 当前卫星索引
            cloud_visibility: 卫星-云可见性矩阵 (72, 5)
            cloud_distances: 卫星-云距离矩阵 (72, 5)
            
        Returns:
            selected_clouds: 选中的云中心索引列表
            count: 实际可见云中心数量
        """
        # 获取可见云中心
        visible_clouds = np.where(cloud_visibility[satellite_idx])[0]
        
        if len(visible_clouds) == 0:
            return [], 0
        
        # 获取到可见云中心的距离
        distances_to_clouds = cloud_distances[satellite_idx, visible_clouds]
        
        # 应用距离阈值
        within_threshold = distances_to_clouds <= self.cloud_distance_threshold
        valid_clouds = visible_clouds[within_threshold]
        valid_distances = distances_to_clouds[within_threshold]
        
        if len(valid_clouds) == 0:
            return [], 0
        
        # 按距离排序，选择最近的N个
        sorted_indices = np.argsort(valid_distances)
        selected_count = min(len(valid_clouds), self.max_clouds)
        selected_clouds = valid_clouds[sorted_indices[:selected_count]].tolist()
        
        return selected_clouds, len(valid_clouds)
    
    def batch_compute_action_spaces(
        self,
        visibility_matrices: Dict[str, np.ndarray],
        distances: Dict[str, np.ndarray],
        num_satellites: int = 72
    ) -> Tuple[List[Dict], np.ndarray]:
        """
        批量计算所有卫星的动作空间（优化版本）
        
        Args:
            visibility_matrices: 可见性矩阵字典
            distances: 距离矩阵字典
            num_satellites: 卫星数量
            
        Returns:
            all_mappings: 所有卫星的动作映射列表
            all_masks: 所有卫星的动作掩码 (72, 9)
        """
        all_mappings = []
        all_masks = np.zeros((num_satellites, self.max_actions), dtype=bool)
        
        for sat_idx in range(num_satellites):
            mapping, mask = self.compute_action_space(
                sat_idx,
                visibility_matrices,
                distances
            )
            all_mappings.append(mapping)
            all_masks[sat_idx] = mask
        
        return all_mappings, all_masks
    
    def validate_action(
        self,
        action_idx: int,
        action_mapping: Dict[int, Tuple[str, int]]
    ) -> bool:
        """
        验证动作是否有效
        
        Args:
            action_idx: 动作索引
            action_mapping: 当前的动作映射
            
        Returns:
            是否有效
        """
        return action_idx in action_mapping
    
    def get_action_target(
        self,
        action_idx: int,
        action_mapping: Dict[int, Tuple[str, int]]
    ) -> Tuple[str, int]:
        """
        获取动作对应的目标
        
        Args:
            action_idx: 动作索引
            action_mapping: 动作映射
            
        Returns:
            (target_type, target_idx) 或 (None, -1) 如果无效
        """
        if action_idx in action_mapping:
            return action_mapping[action_idx]
        return (None, -1)