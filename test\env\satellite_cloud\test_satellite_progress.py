"""
Enhanced test module for satellite compute with accurate progress tracking
Shows real task processing progress across multiple timeslots
"""

import sys
import os
import yaml
import numpy as np
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.env.satellite_cloud.satellite_compute import SatelliteCompute, DPSQScheduler
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus
from src.env.physics_layer.task_models import Task, TaskType
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config


class ProgressTrackingTest:
    """Test with enhanced progress tracking"""
    
    def __init__(self, config_path: str = "src/env/physics_layer/config.yaml"):
        """Initialize test environment"""
        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize logging
        initialize_logging_and_config(config_path)
        
        # Create satellite instance
        self.satellite = SatelliteCompute(
            satellite_id=1,
            config=self.config,
            enable_gpu=False
        )
    
    def create_realistic_tasks(self) -> List[Task]:
        """Create tasks with realistic complexities that span multiple timeslots"""
        tasks = []
        
        # Get CPU frequency to calculate appropriate complexities
        cpu_freq = self.satellite.cpu_frequency  # Hz
        timeslot_duration = 3.0  # seconds
        cycles_per_timeslot = cpu_freq * timeslot_duration
        
        print(f"\n>>> System Parameters:")
        print(f"  CPU Frequency: {cpu_freq/1e12:.3f} THz")
        print(f"  Cycles per timeslot (3s): {cycles_per_timeslot:.2e}")
        print(f"  Max parallel tasks: {self.config['computation'].get('max_parallel_tasks', 100)}")
        
        # Task 1: Should take ~2 timeslots with full CPU
        complexity_1 = cycles_per_timeslot * 2  # 2 timeslots worth of cycles
        data_size_1 = 10.0  # MB
        cycles_per_bit_1 = complexity_1 / (data_size_1 * 8e6)
        
        tasks.append(Task(
            task_id=1,
            type_id=TaskType.REALTIME,
            data_size_mb=data_size_1,
            complexity_cycles_per_bit=int(cycles_per_bit_1),
            deadline_timestamp=30.0,
            priority=1,  # High priority
            location_id=1,
            coordinates=(0.0, 0.0),
            generation_time=0.0
        ))
        print(f"\n  Task 1: {data_size_1}MB, {complexity_1:.2e} cycles (~2 timeslots)")
        
        # Task 2: Should take ~5 timeslots with full CPU
        complexity_2 = cycles_per_timeslot * 5
        data_size_2 = 20.0  # MB
        cycles_per_bit_2 = complexity_2 / (data_size_2 * 8e6)
        
        tasks.append(Task(
            task_id=2,
            type_id=TaskType.NORMAL,
            data_size_mb=data_size_2,
            complexity_cycles_per_bit=int(cycles_per_bit_2),
            deadline_timestamp=60.0,
            priority=2,  # Medium priority
            location_id=2,
            coordinates=(10.0, 10.0),
            generation_time=0.0
        ))
        print(f"  Task 2: {data_size_2}MB, {complexity_2:.2e} cycles (~5 timeslots)")
        
        # Task 3: Should take ~10 timeslots with full CPU
        complexity_3 = cycles_per_timeslot * 10
        data_size_3 = 50.0  # MB
        cycles_per_bit_3 = complexity_3 / (data_size_3 * 8e6)
        
        tasks.append(Task(
            task_id=3,
            type_id=TaskType.COMPUTE_INTENSIVE,
            data_size_mb=data_size_3,
            complexity_cycles_per_bit=int(cycles_per_bit_3),
            deadline_timestamp=100.0,
            priority=3,  # Low priority
            location_id=3,
            coordinates=(20.0, 20.0),
            generation_time=0.0
        ))
        print(f"  Task 3: {data_size_3}MB, {complexity_3:.2e} cycles (~10 timeslots)")
        
        return tasks
    
    def calculate_actual_progress(self, task: ComputeTask) -> float:
        """Calculate actual progress based on remaining complexity"""
        if task.complexity > 0:
            return 1.0 - (task.remaining_complexity / task.complexity)
        return 1.0 if task.remaining_complexity == 0 else 0.0
    
    def test_with_progress_tracking(self):
        """Test with detailed progress tracking"""
        print("\n" + "="*100)
        print("SATELLITE TASK PROCESSING WITH PROGRESS TRACKING")
        print("="*100)
        
        # Reset satellite
        self.satellite.reset()
        
        # Create and add tasks
        tasks = self.create_realistic_tasks()
        
        print("\n>>> Adding tasks to satellite queue:")
        for task in tasks:
            success = self.satellite.add_task(task)
            # Calculate actual complexity
            actual_complexity = task.data_size_mb * 8e6 * task.complexity_cycles_per_bit
            print(f"  Task {task.task_id}: Priority={task.priority}, "
                  f"Complexity={actual_complexity:.2e} cycles - "
                  f"{'[ADDED]' if success else '[REJECTED]'}")
        
        # Process timeslots
        timeslot_duration = 3.0
        num_timeslots = 30
        
        print("\n" + "="*100)
        print("TIMESLOT-BY-TIMESLOT PROCESSING")
        print("="*100)
        
        completed_count = 0
        
        for timeslot in range(num_timeslots):
            self.satellite.current_time = timeslot * timeslot_duration
            
            print(f"\n[TIMESLOT {timeslot}] Time={self.satellite.current_time:.1f}s")
            print("-" * 80)
            
            # Show queue status BEFORE processing
            print("BEFORE PROCESSING:")
            
            if self.satellite.task_queue:
                print("  Queue (waiting):")
                for task in self.satellite.task_queue:
                    print(f"    Task {task.task_id}: Priority={task.priority}, "
                          f"Status={task.status.value}")
            else:
                print("  Queue: Empty")
            
            if self.satellite.processing_tasks:
                print("  Processing (current state):")
                for task in self.satellite.processing_tasks:
                    actual_progress = self.calculate_actual_progress(task)
                    print(f"    Task {task.task_id}: Priority={task.priority}, "
                          f"Progress={actual_progress:.1%}, "
                          f"Remaining={task.remaining_complexity:.2e} cycles")
            else:
                print("  Processing: None")
            
            # Process the timeslot
            print(f"\n>>> Processing timeslot {timeslot}...")
            result = self.satellite.process_timeslot(
                duration=timeslot_duration,
                illuminated=True
            )
            
            # Show what happened
            print("\nAFTER PROCESSING:")
            
            # Check if tasks moved from queue to processing
            if self.satellite.processing_tasks:
                print("  Now processing:")
                for task in self.satellite.processing_tasks:
                    actual_progress = self.calculate_actual_progress(task)
                    
                    # Calculate how many cycles were processed this timeslot
                    if len(self.satellite.processing_tasks) > 0:
                        cycles_per_task = (self.satellite.cpu_frequency * timeslot_duration) / len(self.satellite.processing_tasks)
                    else:
                        cycles_per_task = 0
                    
                    print(f"    Task {task.task_id}: Progress={actual_progress:.1%}, "
                          f"Remaining={task.remaining_complexity:.2e} cycles")
                    
                    if cycles_per_task > 0:
                        estimated_timeslots_remaining = task.remaining_complexity / cycles_per_task
                        print(f"      (Est. {estimated_timeslots_remaining:.1f} more timeslots needed)")
            
            # Show completed tasks
            if result.completed_tasks:
                print("  Completed this timeslot:")
                for task in result.completed_tasks:
                    completed_count += 1
                    print(f"    Task {task.task_id}: COMPLETED")
                    print(f"      Total processing time: {task.accumulated_processing_time:.2f}s")
                    print(f"      Energy consumed: {task.accumulated_energy:.2e} J")
            
            # Show resource usage
            queue_status = self.satellite.get_queue_status()
            print(f"\n  System Status:")
            print(f"    Queue: {queue_status['queue_length']} waiting")
            print(f"    Processing: {queue_status['processing_count']} active")
            print(f"    CPU Utilization: {queue_status['cpu_utilization']:.1%}")
            print(f"    Battery: {queue_status['energy_percentage']:.1f}%")
            
            # Stop if all tasks completed
            if completed_count == len(tasks):
                print("\n>>> All tasks completed! Stopping simulation.")
                break
        
        # Final summary
        print("\n" + "="*100)
        print("FINAL RESULTS")
        print("="*100)
        
        stats = self.satellite.get_statistics()
        print(f"Total tasks processed: {stats['total_tasks_processed']}")
        print(f"Total tasks dropped: {stats['total_tasks_dropped']}")
        print(f"Total energy consumed: {stats['total_energy_consumed']/1e6:.3f} MJ")
        
        print("\nTask Details:")
        for task in self.satellite.completed_tasks:
            print(f"  Task {task.task_id}:")
            print(f"    Complexity: {task.complexity:.2e} cycles")
            print(f"    Processing time: {task.accumulated_processing_time:.2f}s")
            print(f"    Completion time: {task.completion_time:.1f}s")
            if task.arrival_time is not None and task.completion_time is not None:
                total_time = task.completion_time - task.arrival_time
                print(f"    Total time in system: {total_time:.1f}s")
                print(f"    Average throughput: {task.complexity/total_time:.2e} cycles/s")
    
    def test_cpu_allocation(self):
        """Test CPU allocation with 1% granularity"""
        print("\n" + "="*100)
        print("CPU ALLOCATION TEST (1% granularity)")
        print("="*100)
        
        # Reset satellite
        self.satellite.reset()
        
        # Create tasks with specific CPU requirements
        print("\n>>> Creating tasks to test CPU allocation:")
        
        tasks = []
        num_tasks = 10
        
        for i in range(num_tasks):
            # Create tasks with varying sizes
            data_size = 5.0 + i * 2  # 5MB to 23MB
            complexity_per_bit = 1000  # Fixed complexity per bit
            
            task = Task(
                task_id=200 + i,
                type_id=TaskType.NORMAL,
                data_size_mb=data_size,
                complexity_cycles_per_bit=complexity_per_bit,
                deadline_timestamp=100.0,
                priority=1 + (i % 3),  # Priorities 1, 2, 3
                location_id=200 + i,
                coordinates=(float(i), float(i)),
                generation_time=0.0
            )
            tasks.append(task)
            self.satellite.add_task(task)
            
            complexity = data_size * 8e6 * complexity_per_bit
            print(f"  Task {task.task_id}: Size={data_size:.1f}MB, "
                  f"Priority={task.priority}, Complexity={complexity:.2e} cycles")
        
        # Process one timeslot to see CPU allocation
        print("\n>>> Processing with multiple tasks to show CPU allocation:")
        self.satellite.current_time = 0.0
        result = self.satellite.process_timeslot(duration=3.0, illuminated=True)
        
        # Show CPU allocation
        if self.satellite.processing_tasks:
            print(f"\nCPU Allocation (Total {len(self.satellite.processing_tasks)} tasks):")
            
            # With uniform allocation, each task gets 1/N of CPU
            num_processing = len(self.satellite.processing_tasks)
            cpu_per_task = 100.0 / num_processing  # Percentage
            
            for i, task in enumerate(self.satellite.processing_tasks):
                # Show CPU allocation with 1% granularity
                allocated_cpu_percent = cpu_per_task
                print(f"  Task {task.task_id}: {allocated_cpu_percent:.1f}% CPU "
                      f"(Priority={task.priority})")
            
            print(f"\nTotal CPU allocated: {num_processing * cpu_per_task:.1f}%")
            print(f"CPU allocation granularity: 1% increments")
            print(f"Each task receives: {cpu_per_task:.1f}% of total CPU")


def main():
    """Main test execution"""
    # Create test instance
    tester = ProgressTrackingTest()
    
    # Run main progress tracking test
    tester.test_with_progress_tracking()
    
    # Run CPU allocation test
    tester.test_cpu_allocation()


if __name__ == "__main__":
    main()