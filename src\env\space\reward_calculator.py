"""
奖励计算模块

基于Dec-POMDP模型计算多维度奖励
"""

import numpy as np
from typing import Dict, List, Optional, Any


class RewardCalculator:
    """
    奖励计算器
    
    计算任务完成、能耗、延迟和负载均衡等多维度奖励
    """
    
    def __init__(self, config: Dict, space_config: Dict, 
                 load_balancing_calculator=None, regions=None):
        """
        初始化奖励计算器
        
        Args:
            config: SPACE2主配置
            space_config: PettingZoo环境配置
            load_balancing_calculator: 负载均衡计算器
            regions: 区域定义数据
        """
        self.config = config
        self.reward_config = space_config['space']['reward']
        self.load_balancing_calculator = load_balancing_calculator
        self.regions = regions if regions else []
        
        # 奖励权重参数
        self.alpha_1 = self.reward_config['alpha_1']  # 高优先级任务完成奖励
        self.alpha_2 = self.reward_config['alpha_2']  # 中优先级任务完成奖励
        self.alpha_3 = self.reward_config['alpha_3']  # 低优先级任务完成奖励
        self.beta = self.reward_config['beta']  # 能耗惩罚系数
        self.zeta = self.reward_config['zeta']  # 延迟惩罚系数
        self.delta = self.reward_config['delta']  # 负载不均衡惩罚系数
        self.timeout_penalty = self.reward_config['timeout_penalty']  # 超时惩罚
        
    def calculate(
        self,
        agent_id: str,
        computation_results: Dict,
        action_mapping: Dict
    ) -> float:
        """
        计算单个智能体的奖励
        
        Args:
            agent_id: 智能体ID
            computation_results: 计算结果字典
            action_mapping: 动作映射（用于协作奖励）
            
        Returns:
            总奖励值
        """
        satellite_idx = int(agent_id.split('_')[1])
        
        # 计算个体奖励
        individual_reward = self._calculate_individual_reward(
            satellite_idx,
            computation_results
        )
        
        # 计算区域协作奖励
        regional_reward = self._calculate_regional_reward(
            satellite_idx,
            computation_results
        )
        
        # 总奖励
        total_reward = individual_reward + regional_reward
        
        return total_reward
    
    def _calculate_individual_reward(
        self,
        satellite_idx: int,
        results: Dict
    ) -> float:
        """
        计算个体奖励
        
        包括：任务完成奖励、能耗惩罚、延迟惩罚
        
        Args:
            satellite_idx: 卫星索引
            results: 计算结果
            
        Returns:
            个体奖励值
        """
        reward = 0.0
        
        # 1. 任务完成奖励（按优先级）
        if 'completed_tasks' in results:
            completed = results['completed_tasks'].get(satellite_idx, [])
            for task in completed:
                priority = getattr(task, 'priority', 2)
                if priority == 1:
                    reward += self.alpha_1
                elif priority == 2:
                    reward += self.alpha_2
                else:
                    reward += self.alpha_3
        
        # 使用备用统计数据
        elif 'task_completion_stats' in results:
            stats = results['task_completion_stats'].get(satellite_idx, {})
            reward += stats.get('high_priority_completed', 0) * self.alpha_1
            reward += stats.get('medium_priority_completed', 0) * self.alpha_2
            reward += stats.get('low_priority_completed', 0) * self.alpha_3
        
        # 2. 能耗惩罚
        if 'energy_consumption' in results:
            energy = results['energy_consumption'].get(satellite_idx, 0)
            reward -= self.beta * energy
        
        # 3. 延迟惩罚
        if 'average_delay' in results:
            delay = results['average_delay'].get(satellite_idx, 0)
            reward -= self.zeta * delay
        
        # 4. 超时任务惩罚
        if 'timeout_tasks' in results:
            timeouts = results['timeout_tasks'].get(satellite_idx, 0)
            reward += self.timeout_penalty * timeouts
        
        return reward
    
    def _calculate_regional_reward(
        self,
        satellite_idx: int,
        results: Dict
    ) -> float:
        """
        计算区域协作奖励
        
        主要考虑负载均衡
        
        Args:
            satellite_idx: 卫星索引
            results: 计算结果
            
        Returns:
            区域奖励值
        """
        reward = 0.0
        
        # 获取区域ID
        region_id = self._get_region_id(satellite_idx)
        
        # 负载均衡奖励
        if 'regional_load_variance' in results:
            variance = results['regional_load_variance'].get(region_id, 0)
            reward -= self.delta * variance
        elif self.load_balancing_calculator is not None:
            # 使用真实的负载均衡计算
            region_satellites = self._get_region_satellites(region_id)
            if region_satellites and 'satellite_loads' in results:
                loads = results['satellite_loads']
                satellite_load_infos = []
                for sat_id in region_satellites:
                    if sat_id in loads:
                        satellite_load_infos.append(loads[sat_id])
                
                if satellite_load_infos:
                    # 使用负载均衡计算器的公式
                    variance = self.load_balancing_calculator.calculate_load_variance(
                        satellite_load_infos
                    )
                    reward -= self.delta * variance
        
        return reward
    
    def _get_region_id(self, satellite_idx: int) -> int:
        """
        获取卫星所属区域ID（使用真实区域划分）
        
        Args:
            satellite_idx: 卫星索引
            
        Returns:
            区域ID
        """
        if self.load_balancing_calculator is not None:
            # 使用负载均衡计算器的方法
            return self.load_balancing_calculator.get_satellite_region(satellite_idx)
        elif hasattr(self, '_satellite_positions') and satellite_idx in self._satellite_positions:
            # 根据卫星位置匹配区域
            sat_pos = self._satellite_positions[satellite_idx]
            return self._match_region_by_position(sat_pos['latitude'], sat_pos['longitude'])
        else:
            # 备用：使用基于24个区域的划分
            # 24个区域 = 3个纬度带 x 8个经度带
            return (satellite_idx % 24) + 1
    
    def _match_region_by_position(self, latitude: float, longitude: float) -> int:
        """
        根据经纬度匹配区域（使用regions.json数据）
        24个区域：4个纬度带×6个经度带
        """
        if not self.regions:
            # 备用：基于简单划分
            return self._simple_region_mapping(latitude, longitude)
            
        for region in self.regions:
            lat_range = region['latitude_range']
            lon_range = region['longitude_range']
            
            if (lat_range['min'] <= latitude <= lat_range['max'] and
                lon_range['min'] <= longitude <= lon_range['max']):
                return region['region_id']
        
        # 默认返回区域1
        return 1
    
    def _simple_region_mapping(self, latitude: float, longitude: float) -> int:
        """
        简单的区域映射备用方案
        """
        # 4个纬度带：[-60,-30), [-30,0), [0,30), [30,60]
        # 6个经度带：每60度一个带
        
        lat_band = 0
        if latitude >= 30:
            lat_band = 0
        elif latitude >= 0:
            lat_band = 1  
        elif latitude >= -30:
            lat_band = 2
        else:
            lat_band = 3
            
        lon_band = int((longitude + 180) // 60)
        lon_band = min(max(lon_band, 0), 5)
        
        return lat_band * 6 + lon_band + 1
    
    def _get_region_satellites(self, region_id: int) -> List[int]:
        """
        获取区域内的所有卫星（使用真实区域划分）
        
        Args:
            region_id: 区域ID
            
        Returns:
            卫星索引列表
        """
        if self.load_balancing_calculator is not None:
            # 使用负载均衡计算器获取区域内卫星
            return self.load_balancing_calculator.get_region_satellites(region_id)
        else:
            # 备用：遍历所有卫星，找到属于该区域的
            satellites_in_region = []
            for sat_idx in range(72):
                if self._get_region_id(sat_idx) == region_id:
                    satellites_in_region.append(sat_idx)
            return satellites_in_region
    
    def calculate_batch(
        self,
        computation_results: Dict,
        action_mappings: Dict[str, Dict]
    ) -> Dict[str, float]:
        """
        批量计算所有智能体的奖励
        
        Args:
            computation_results: 计算结果
            action_mappings: 所有智能体的动作映射
            
        Returns:
            奖励字典 {agent_id: reward}
        """
        rewards = {}
        
        for agent_id in action_mappings.keys():
            rewards[agent_id] = self.calculate(
                agent_id,
                computation_results,
                action_mappings.get(agent_id, {})
            )
        
        return rewards
    
    def get_reward_components(
        self,
        agent_id: str,
        computation_results: Dict
    ) -> Dict[str, float]:
        """
        获取奖励的各个组成部分（用于分析）
        
        Args:
            agent_id: 智能体ID
            computation_results: 计算结果
            
        Returns:
            奖励组成字典
        """
        satellite_idx = int(agent_id.split('_')[1])
        
        components = {
            'task_completion': 0.0,
            'energy_penalty': 0.0,
            'delay_penalty': 0.0,
            'timeout_penalty': 0.0,
            'load_balancing': 0.0
        }
        
        # 任务完成奖励
        if 'task_completion_stats' in computation_results:
            stats = computation_results['task_completion_stats'].get(satellite_idx, {})
            components['task_completion'] = (
                stats.get('high_priority_completed', 0) * self.alpha_1 +
                stats.get('medium_priority_completed', 0) * self.alpha_2 +
                stats.get('low_priority_completed', 0) * self.alpha_3
            )
        
        # 能耗惩罚 - 支持新的指标系统
        energy = 0.0
        if 'energy_consumption' in computation_results:
            energy = computation_results['energy_consumption'].get(satellite_idx, 0)
        elif 'unified_metrics' in computation_results:
            # 从统一指标系统获取能耗数据
            unified = computation_results['unified_metrics']
            if 'energy_metrics' in unified and 'energy_metrics' in unified['energy_metrics']:
                energy = unified['energy_metrics']['energy_metrics'].total_energy / len(computation_results.get('satellites', [1]))
        
        components['energy_penalty'] = -self.beta * energy
        
        # 延迟惩罚 - 支持新的指标系统
        delay = 0.0
        if 'average_delay' in computation_results:
            delay = computation_results['average_delay'].get(satellite_idx, 0)
        elif 'unified_metrics' in computation_results:
            # 从统一指标系统获取延迟数据
            unified = computation_results['unified_metrics']
            if 'latency_metrics' in unified and 'satellite_based' in unified['latency_metrics']:
                sat_latency = unified['latency_metrics']['satellite_based']
                if 'end_to_end_latency' in sat_latency:
                    delay = sat_latency['end_to_end_latency'].mean
        
        components['delay_penalty'] = -self.zeta * delay
        
        # 超时惩罚
        if 'timeout_tasks' in computation_results:
            timeouts = computation_results['timeout_tasks'].get(satellite_idx, 0)
            components['timeout_penalty'] = self.timeout_penalty * timeouts
        
        # 负载均衡
        region_id = self._get_region_id(satellite_idx)
        if 'regional_load_variance' in computation_results:
            variance = computation_results['regional_load_variance'].get(region_id, 0)
            components['load_balancing'] = -self.delta * variance
        
        return components