"""
随机卸载策略Agent
为每个任务随机选择卸载目标（本地、邻居、云、丢弃）
"""

import numpy as np
from typing import Dict, Any
import random


class RandomAgent:
    """
    随机策略Agent
    根据动作掩码随机选择可用动作
    """
    
    def __init__(self, agent_id: str, seed: int = None):
        """
        初始化随机Agent
        
        Args:
            agent_id: 智能体ID
            seed: 随机种子
        """
        self.agent_id = agent_id
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)
    
    def select_action(self, observation: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """
        根据观测选择动作
        
        Args:
            observation: 环境观测
            
        Returns:
            动作字典，包含offloading_decisions和resource_allocation
        """
        # 获取动作掩码和任务掩码
        action_mask = observation['action_mask']  # (9,) 可用动作
        task_mask = observation['task_mask']  # (100,) 有效任务
        
        # 获取有效动作索引
        valid_actions = np.where(action_mask)[0]
        
        # 为每个任务随机选择卸载决策
        offloading_decisions = np.zeros(100, dtype=np.int32)
        
        for i in range(100):
            if task_mask[i] > 0:  # 如果任务有效
                # 从有效动作中随机选择
                if len(valid_actions) > 0:
                    offloading_decisions[i] = np.random.choice(valid_actions)
                else:
                    offloading_decisions[i] = 0  # 默认本地处理
            else:
                offloading_decisions[i] = 0  # 无效任务默认为0
        
        # 资源分配：随机分配0.1到1.0之间的资源
        resource_allocation = np.random.uniform(0.1, 1.0, size=100).astype(np.float32)
        
        # 对无效任务设置资源为0
        resource_allocation[task_mask == 0] = 0.0
        
        return {
            'offloading_decisions': offloading_decisions,
            'resource_allocation': resource_allocation
        }
    
    def reset(self):
        """重置Agent状态（随机Agent无状态）"""
        pass
    
    def update(self, *args, **kwargs):
        """更新Agent（随机Agent不需要更新）"""
        pass


class RandomMultiAgent:
    """
    管理多个随机Agent的类
    """
    
    def __init__(self, num_agents: int = 72, seed: int = None):
        """
        初始化多Agent系统
        
        Args:
            num_agents: Agent数量
            seed: 随机种子
        """
        self.num_agents = num_agents
        self.agents = {}
        
        for i in range(num_agents):
            agent_id = f"satellite_{i}"
            # 每个agent使用不同的种子
            agent_seed = None if seed is None else seed + i
            self.agents[agent_id] = RandomAgent(agent_id, agent_seed)
    
    def select_actions(self, observations: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        为所有Agent选择动作
        
        Args:
            observations: 所有Agent的观测
            
        Returns:
            所有Agent的动作
        """
        actions = {}
        for agent_id, obs in observations.items():
            if agent_id in self.agents:
                actions[agent_id] = self.agents[agent_id].select_action(obs)
        return actions
    
    def reset(self):
        """重置所有Agent"""
        for agent in self.agents.values():
            agent.reset()