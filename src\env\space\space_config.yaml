# PettingZoo环境专用配置文件
# 定义观测空间、动作空间、奖励权重等参数

space:
  # 观测空间参数
  observation:
    max_neighbors: 6         # 最大邻居卫星数量（文档要求：最多6个）
    max_clouds: 1            # 最大云中心数量（文档要求：1个）
    max_tasks: 35            # 最大任务队列长度
    self_state_dim: 11       # 自身状态维度
    neighbor_state_dim: 6    # 每个邻居状态维度
    cloud_state_dim: 5       # 云中心状态维度
    task_feature_dim: 9      # 每个任务特征维度
    
  # 动作空间参数  
  action:
    max_actions: 9           # 最大动作数（1本地+6邻居+1云+1丢弃）
    num_local: 1            # 本地处理选项数
    num_neighbors: 6        # 邻居卫星选项数（最多6个）
    num_clouds: 1           # 云中心选项数（最多1个）
    num_drop: 1             # 丢弃选项数
    enable_resource_allocation: true  # 是否启用资源分配
    
  # 动态动作空间配置
  dynamic_action:
    satellite_distance_threshold_km: 5500  # 卫星间可见距离阈值
    cloud_distance_threshold_km: 3300      # 卫星-云可见距离阈值
    select_nearest: true                   # 是否选择最近的目标
    
  # 奖励权重（基于Dec-POMDP模型）
  reward:
    # 个体奖励权重
    alpha_1: 10.0           # 高优先级任务完成奖励
    alpha_2: 6.0            # 中优先级任务完成奖励
    alpha_3: 3.0            # 低优先级任务完成奖励
    beta: 0.1               # 能耗惩罚系数
    zeta: 0.01              # 延迟惩罚系数
    timeout_penalty: -5.0   # 任务超时惩罚
    
    # 区域协作奖励权重
    delta: 0.5              # 负载不均衡惩罚系数
    
  # 环境参数
  environment:
    max_episode_steps: 1500  # 最大回合步数（修改为1500）
    auto_reset: false        # 是否自动重置
    seed: 42                # 随机种子