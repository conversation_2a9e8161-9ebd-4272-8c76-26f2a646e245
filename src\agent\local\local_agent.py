"""
全部本地运行策略Agent
所有任务都在本地处理，资源平均分配
"""

import numpy as np
from typing import Dict, Any


class LocalAgent:
    """
    本地处理策略Agent
    所有任务都选择本地处理（动作0）
    资源平均分配给所有有效任务
    """
    
    def __init__(self, agent_id: str):
        """
        初始化本地Agent
        
        Args:
            agent_id: 智能体ID
        """
        self.agent_id = agent_id
    
    def select_action(self, observation: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """
        根据观测选择动作
        所有任务都选择动作0（本地处理）
        
        Args:
            observation: 环境观测
            
        Returns:
            动作字典，包含offloading_decisions和resource_allocation
        """
        # 获取任务掩码
        task_mask = observation['task_mask']  # (100,) 有效任务
        
        # 所有任务都选择动作0（本地处理）
        offloading_decisions = np.zeros(100, dtype=np.int32)
        
        # 计算有效任务数量
        num_valid_tasks = np.sum(task_mask > 0)
        
        # 资源平均分配
        resource_allocation = np.zeros(100, dtype=np.float32)
        
        if num_valid_tasks > 0:
            # 每个有效任务分配相等的资源
            resource_per_task = 1.0 / num_valid_tasks
            resource_allocation[task_mask > 0] = resource_per_task
        
        return {
            'offloading_decisions': offloading_decisions,
            'resource_allocation': resource_allocation
        }
    
    def reset(self):
        """重置Agent状态（本地Agent无状态）"""
        pass
    
    def update(self, *args, **kwargs):
        """更新Agent（本地Agent不需要更新）"""
        pass


class LocalMultiAgent:
    """
    管理多个本地处理Agent的类
    """
    
    def __init__(self, num_agents: int = 72):
        """
        初始化多Agent系统
        
        Args:
            num_agents: Agent数量
        """
        self.num_agents = num_agents
        self.agents = {}
        
        for i in range(num_agents):
            agent_id = f"satellite_{i}"
            self.agents[agent_id] = LocalAgent(agent_id)
    
    def select_actions(self, observations: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        为所有Agent选择动作
        
        Args:
            observations: 所有Agent的观测
            
        Returns:
            所有Agent的动作
        """
        actions = {}
        for agent_id, obs in observations.items():
            if agent_id in self.agents:
                actions[agent_id] = self.agents[agent_id].select_action(obs)
        return actions
    
    def reset(self):
        """重置所有Agent"""
        for agent in self.agents.values():
            agent.reset()