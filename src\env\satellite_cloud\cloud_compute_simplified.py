"""
Simplified Cloud Compute - Treated as a powerful satellite
Cloud is essentially a satellite with:
- 10x computing power
- No energy constraints
- Larger queue capacity
"""

from typing import Dict, Optional
import yaml

# Reuse SatelliteCompute with modified parameters
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.env.Foundation_Layer.logging_config import get_logger


class CloudCompute(SatelliteCompute):
    """
    Cloud compute center implemented as a powerful satellite
    Inherits from SatelliteCompute but with enhanced capabilities
    """
    
    def __init__(self, cloud_id: int, config_path: str = None, 
                 config: Dict = None):
        """
        Initialize cloud as a powerful satellite
        
        Args:
            cloud_id: Cloud center ID (use satellite_id internally)
            config_path: Path to configuration file
            config: Configuration dictionary
        """
        # Load configuration
        if config is not None:
            cloud_config = config.copy()
        elif config_path is not None:
            with open(config_path, 'r', encoding='utf-8') as f:
                cloud_config = yaml.safe_load(f)
        else:
            raise ValueError("Either config_path or config must be provided")
        
        # Modify configuration for cloud (10x more powerful than satellite)
        # Cloud has 2x CPU frequency in config, make it 10x total computing power
        computation_config = cloud_config['computation']
        
        # Original satellite frequency
        sat_freq = computation_config['f_leo_hz']
        
        # Cloud frequency: 10x satellite frequency for 10x computing power
        computation_config['f_leo_hz'] = sat_freq * 10  # 10x computing power
        
        # No energy constraints for cloud (set very high battery)
        computation_config['leo_battery_capacity_j'] = 1e15  # Essentially unlimited
        computation_config['leo_solar_power_w'] = 1e10  # Always charged
        
        # Larger queue capacity (10x satellite)
        cloud_config['queuing']['max_queue_size'] = cloud_config['queuing']['max_queue_size'] * 10
        
        # More parallel tasks capability
        computation_config['max_parallel_tasks'] = 1000  # 10x satellite capacity
        
        # Initialize as a super-powered satellite
        # Use negative ID to distinguish from regular satellites
        super().__init__(
            satellite_id=-cloud_id,  # Negative ID for cloud
            config=cloud_config,
            enable_gpu=True  # Enable GPU for cloud by default
        )
        
        # Override some attributes
        self.cloud_id = cloud_id
        self.is_cloud = True
        
        # Cloud specific logger
        self.logger = get_logger(f"CloudCompute_{cloud_id}")
        self.logger.info(f"Initialized Cloud {cloud_id} as 10x powerful satellite")
        self.logger.info(f"  CPU Frequency: {self.cpu_frequency/1e12:.2f} THz")
        self.logger.info(f"  Max Queue: {self.scheduler.max_queue_size}")
        self.logger.info(f"  Max Parallel Tasks: {computation_config['max_parallel_tasks']}")
    
    def process_timeslot(self, duration: float, illuminated: bool = True) -> any:
        """
        Process tasks for one timeslot
        Cloud always has power (illuminated doesn't matter)
        
        Args:
            duration: Timeslot duration in seconds
            illuminated: Ignored for cloud (always has power)
            
        Returns:
            ProcessingResult with completed tasks and metrics
        """
        # Cloud is always "illuminated" (has unlimited power)
        return super().process_timeslot(duration, illuminated=True)
    
    def can_accept_task(self, task) -> bool:
        """
        Check if cloud can accept task
        Cloud has no energy constraints, only queue capacity
        
        Args:
            task: Task to check
            
        Returns:
            True if can accept
        """
        # Only check queue capacity, ignore energy
        return len(self.task_queue) < self.scheduler.max_queue_size
    
    def get_statistics(self) -> Dict:
        """Get cloud statistics"""
        stats = super().get_statistics()
        
        # Add cloud-specific info
        stats['node_type'] = 'cloud'
        stats['cloud_id'] = self.cloud_id
        stats['computing_power_ratio'] = 10  # 10x satellite
        
        return stats
    
    def __str__(self) -> str:
        """String representation"""
        return f"CloudCompute_{self.cloud_id} (10x Satellite)"