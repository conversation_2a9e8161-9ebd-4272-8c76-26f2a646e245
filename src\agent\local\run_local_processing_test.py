"""
本地处理测试运行文件

使用本地处理策略验证SPACE环境，输出核心性能指标：
1. 平均每个任务的延迟
2. 平均每个任务的能耗
3. 奖励
4. 全局负载均衡
5. 不同任务的完成率
"""

# 首先添加项目根目录到Python路径
import sys
import os
from pathlib import Path

# 获取项目根目录 (SPACE4目录)
# 直接使用当前工作目录作为项目根目录
project_root = Path(os.getcwd())
sys.path.insert(0, str(project_root))

# 验证路径设置（简化输出）
src_dir = project_root / "src"
if not src_dir.exists():
    print(f"警告: src目录不存在于 {project_root}")

import time
import logging
import numpy as np
from datetime import datetime
from typing import Dict, Any

# 导入SPACE环境和本地处理策略
from src.env.space.space_env import SpaceParallelEnv
from src.agent.local.local_processing_agent import LocalProcessingStrategy, MetricsCollector


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def run_local_processing_test(max_steps: int = 10, verbose: bool = True):
    """
    运行本地处理测试
    
    Args:
        max_steps: 最大运行步数
        verbose: 是否输出详细信息
        
    Returns:
        性能指标字典
    """
    logger = setup_logging()
    
    if verbose:
        print("=" * 60)
        print("SPACE2 本地处理策略验证测试")
        print("=" * 60)
        print("策略：所有任务本地处理，CPU资源平分")
        print(f"运行步数：{max_steps}")
        print("=" * 60)
    
    try:
        # 初始化环境
        logger.info("初始化SPACE2环境...")
        env = SpaceParallelEnv()
        
        # 初始化策略和指标收集器
        strategies = {}
        metrics_collector = MetricsCollector(num_agents=72, config=env.config)
        
        # 为每个智能体创建本地处理策略
        for agent_id in env.agents:
            strategies[agent_id] = LocalProcessingStrategy()
        
        logger.info("初始化完成，开始测试...")
        
        # 重置环境
        observations, infos = env.reset()
        
        # 运行测试
        for step in range(max_steps):
            if verbose:
                print(f"步骤 {step+1}/{max_steps} - 开始处理")
            
            step_start_time = time.time()
            
            # 生成所有智能体的动作
            if verbose:
                print(f"  - 生成动作中...")
            actions = {}
            for agent_id in env.agents:
                if agent_id in observations:
                    action = strategies[agent_id].get_action(observations[agent_id], agent_id)
                    actions[agent_id] = action
            
            if verbose:
                print(f"  - 执行环境步骤中...")
            # 执行动作
            observations, rewards, terminations, truncations, infos = env.step(actions)
            
            step_time = time.time() - step_start_time
            if verbose:
                print(f"  - 步骤完成，耗时: {step_time:.2f}秒")
            
            # 更新指标 - 传递环境组件获取真实数据
            metrics_collector.update(rewards, env.satellites_compute, env.compute_manager, env.current_timeslot)
            
            # 更新各智能体统计
            for agent_id, reward in rewards.items():
                if agent_id in strategies:
                    strategies[agent_id].update_stats(reward, infos.get(agent_id, {}))
            
            # 检查是否结束
            if any(terminations.values()) or any(truncations.values()):
                logger.info(f"环境在第{step+1}步结束")
                break
        
        # 获取最终指标
        final_metrics = metrics_collector.get_final_metrics()
        
        if verbose:
            print("\n" + "=" * 60)
            print("测试完成 - 性能指标报告")
            print("=" * 60)
            
            print(f"1. 平均任务延迟: {final_metrics['average_task_delay']:.6f} 秒")
            print(f"2. 平均任务能耗: {final_metrics['average_task_energy']:.6f} 焦耳")
            print(f"3. 平均奖励: {final_metrics['average_reward']:.6f}")
            print(f"4. 全局负载均衡: {final_metrics['global_load_balance']:.6f}")
            print(f"5. 总体完成率: {final_metrics['overall_completion_rate']:.6f}")
            
            print("\n详细统计:")
            print(f"- 总运行步数: {step+1}")
            print(f"- 总处理任务数: {metrics_collector.global_stats['total_tasks']}")
            print(f"- 总累计奖励: {metrics_collector.global_stats['total_reward']:.6f}")
            
            print("=" * 60)
        
        # 清理资源
        env.close()
        
        return final_metrics
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise


def save_results_to_file(metrics: Dict[str, Any], filename: str = None):
    """
    保存结果到文件
    
    Args:
        metrics: 性能指标
        filename: 文件名
    """
    if filename is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"local_processing_test_results_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("SPACE2 本地处理策略测试结果\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("核心性能指标:\n")
        f.write("-" * 30 + "\n")
        f.write(f"1. 平均任务延迟: {metrics['average_task_delay']:.6f} 秒\n")
        f.write(f"2. 平均任务能耗: {metrics['average_task_energy']:.6f} 焦耳\n")
        f.write(f"3. 平均奖励: {metrics['average_reward']:.6f}\n")
        f.write(f"4. 全局负载均衡: {metrics['global_load_balance']:.6f}\n")
        f.write(f"5. 总体完成率: {metrics['overall_completion_rate']:.6f}\n")
        
        f.write("\n策略说明:\n")
        f.write("- 所有任务均采用本地处理，不进行任何卸载\n")
        f.write("- CPU资源在所有排队任务间平均分配\n")
        f.write("- 用于验证SPACE2环境的基本功能和正确性\n")
    
    print(f"结果已保存到: {filename}")


def main():
    """主函数"""
    print("SPACE2 本地处理策略验证测试")
    print("本测试用于验证环境正确性，采用最简单的本地处理策略")
    
    try:
        # 运行测试
        start_time = time.time()
        metrics = run_local_processing_test(max_steps=10, verbose=True)
        total_time = time.time() - start_time
        
        print(f"\n测试成功完成！")
        print(f"总耗时: {total_time:.2f} 秒")
        
        # 保存结果
        save_results_to_file(metrics)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()