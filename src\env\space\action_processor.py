"""
动作处理模块

处理智能体动作，确保任务的原子性（不可分割）
"""

import numpy as np
from typing import Dict, List, Optional, Any


class ActionProcessor:
    """
    动作处理器
    
    负责验证和执行智能体动作，保证任务完整性
    """
    
    def __init__(self, config: Dict, space_config: Dict):
        """
        初始化动作处理器
        
        Args:
            config: SPACE2主配置
            space_config: PettingZoo环境配置
        """
        self.config = config
        self.space_config = space_config['space']
        
        # 动作空间参数
        self.max_actions = self.space_config['action']['max_actions']  # 9
        self.max_tasks = self.space_config['observation']['max_tasks']  # 35
        
    def process(
        self,
        satellite_idx: int,
        action: Dict[str, np.ndarray],
        action_mapping: Dict[int, tuple],
        compute_manager: Any,
        timeslot: int,
        satellite_compute: Any = None
    ):
        """
        处理单个卫星的动作
        
        重要：确保任务的原子性，任务必须完整地在一个位置处理
        
        Args:
            satellite_idx: 卫星索引
            action: 动作字典
                - 'offloading_decisions': (100,) 每个任务的卸载决策（0-8）
                - 'resource_allocation': (100,) 每个任务的资源分配
            action_mapping: 当前时刻的动作映射
            compute_manager: 计算管理器
            timeslot: 当前时隙
            satellite_compute: 卫星计算组件（可选）
        """
        if not action or 'offloading_decisions' not in action:
            return
        
        offloading_decisions = action['offloading_decisions']
        resource_allocation = action.get('resource_allocation', np.ones(self.max_tasks) * 0.1)
        
        # 获取实际任务队列 - 优先从satellite_compute获取
        if satellite_compute and hasattr(satellite_compute, 'task_queue'):
            task_queue = satellite_compute.task_queue
        else:
            # 备用：返回空队列
            task_queue = []
        real_queue_len = len(task_queue)
        
        # 处理每个任务
        for task_idx in range(min(real_queue_len, len(offloading_decisions))):
            task = task_queue[task_idx]
            decision = int(offloading_decisions[task_idx])
            
            # 验证动作有效性和任务原子性
            if not self.validate_action(decision, action_mapping):
                # 无效动作，降级为本地处理
                self._handle_invalid_action(task, satellite_idx, compute_manager, "invalid_action")
                continue
                
            if not self.validate_task_atomicity(task):
                # 任务原子性检查失败，降级为本地处理
                self._handle_invalid_action(task, satellite_idx, compute_manager, "atomicity_violation")
                continue
            
            # 获取目标
            target_type, target_idx = action_mapping[decision]
            
            # 进一步验证目标有效性
            if not self._validate_target_availability(target_type, target_idx, compute_manager):
                # 目标不可用，降级为本地处理
                self._handle_invalid_action(task, satellite_idx, compute_manager, "target_unavailable")
                continue
            
            # 根据目标类型执行动作
            try:
                self._execute_task_action(
                    task,
                    target_type,
                    target_idx,
                    satellite_idx,
                    resource_allocation[task_idx],
                    compute_manager,
                    timeslot
                )
            except Exception as e:
                # 执行失败，降级为本地处理
                self._handle_invalid_action(task, satellite_idx, compute_manager, f"execution_error: {str(e)}")
                continue
    
    def _execute_task_action(
        self,
        task: Any,
        target_type: str,
        target_idx: int,
        source_satellite_idx: int,
        cpu_allocation: float,
        compute_manager: Any,
        timeslot: int
    ):
        """
        执行任务动作，确保任务原子性
        
        Args:
            task: 任务对象
            target_type: 目标类型 ('local', 'satellite', 'cloud')
            target_idx: 目标索引
            source_satellite_idx: 源卫星索引
            cpu_allocation: CPU资源分配比例
            compute_manager: 计算管理器
            timeslot: 当前时隙
        """
        if target_type == 'local':
            # 本地处理：任务在本卫星完整处理
            self._process_locally(
                task,
                source_satellite_idx,
                cpu_allocation,
                compute_manager
            )
            
        elif target_type == 'satellite':
            # 卫星迁移：任务完整迁移到目标卫星
            self._migrate_to_satellite(
                task,
                source_satellite_idx,
                target_idx,
                compute_manager,
                timeslot
            )
            
        elif target_type == 'cloud':
            # 云卸载：任务完整卸载到云中心
            self._offload_to_cloud(
                task,
                source_satellite_idx,
                target_idx,
                compute_manager,
                timeslot
            )
            
        elif target_type == 'drop':
            # 丢弃任务（动作8）
            self._drop_task(
                task,
                source_satellite_idx,
                compute_manager
            )
            
        else:
            # 其他未知情况，默认丢弃
            self._drop_task(
                task,
                source_satellite_idx,
                compute_manager
            )
    
    def _process_locally(
        self,
        task: Any,
        satellite_idx: int,
        cpu_allocation: float,
        compute_manager: Any
    ):
        """
        本地处理任务
        
        Args:
            task: 任务对象
            satellite_idx: 卫星索引
            cpu_allocation: CPU分配比例
            compute_manager: 计算管理器
        """
        # 确保CPU分配在合理范围内
        cpu_allocation = np.clip(cpu_allocation, 0.1, 1.0)
        
        # 调用计算管理器的本地处理方法
        if hasattr(compute_manager, 'process_task_locally'):
            compute_manager.process_task_locally(
                satellite_idx,
                task,
                cpu_allocation
            )
        else:
            # 备用处理逻辑
            task.status = 'processing'
            task.processing_location = ('satellite', satellite_idx)
            task.allocated_cpu = cpu_allocation
    
    def _migrate_to_satellite(
        self,
        task: Any,
        source_idx: int,
        target_idx: int,
        compute_manager: Any,
        timeslot: int
    ):
        """
        将任务完整迁移到目标卫星
        
        重要：任务必须完整迁移，不能分割
        
        Args:
            task: 任务对象
            source_idx: 源卫星索引
            target_idx: 目标卫星索引
            compute_manager: 计算管理器
            timeslot: 当前时隙
        """
        # 验证任务原子性
        assert not hasattr(task, 'is_split') or not task.is_split, \
            "任务不可分割，必须完整迁移"
        
        # 调用计算管理器的迁移方法
        if hasattr(compute_manager, 'migrate_complete_task'):
            compute_manager.migrate_complete_task(
                task,
                source_idx,
                target_idx,
                timeslot
            )
        else:
            # 备用迁移逻辑
            # 从源卫星队列移除
            if hasattr(compute_manager, 'remove_task'):
                compute_manager.remove_task(source_idx, task)
            
            # 添加到目标卫星队列
            if hasattr(compute_manager, 'add_task'):
                compute_manager.add_task(target_idx, task)
            
            # 更新任务状态
            task.status = 'migrating'
            task.migration_source = source_idx
            task.migration_target = target_idx
            task.migration_time = timeslot
    
    def _offload_to_cloud(
        self,
        task: Any,
        source_idx: int,
        cloud_idx: int,
        compute_manager: Any,
        timeslot: int
    ):
        """
        将任务完整卸载到云中心
        
        Args:
            task: 任务对象
            source_idx: 源卫星索引
            cloud_idx: 云中心索引
            compute_manager: 计算管理器
            timeslot: 当前时隙
        """
        # 验证任务原子性
        assert not hasattr(task, 'is_split') or not task.is_split, \
            "任务不可分割，必须完整卸载"
        
        # 调用计算管理器的云卸载方法
        if hasattr(compute_manager, 'offload_complete_task'):
            compute_manager.offload_complete_task(
                task,
                source_idx,
                cloud_idx,
                timeslot
            )
        else:
            # 备用卸载逻辑
            task.status = 'offloading'
            task.offload_source = source_idx
            task.offload_target = cloud_idx
            task.offload_time = timeslot
            task.processing_location = ('cloud', cloud_idx)
    
    def _drop_task(
        self,
        task: Any,
        satellite_idx: int,
        compute_manager: Any
    ):
        """
        丢弃任务
        
        Args:
            task: 任务对象
            satellite_idx: 卫星索引
            compute_manager: 计算管理器
        """
        if hasattr(compute_manager, 'drop_task'):
            compute_manager.drop_task(satellite_idx, task)
        else:
            task.status = 'dropped'
            task.drop_location = satellite_idx
    
    def validate_action(
        self,
        action_idx: int,
        action_mapping: Dict[int, tuple]
    ) -> bool:
        """
        验证动作是否有效
        
        Args:
            action_idx: 动作索引
            action_mapping: 动作映射
            
        Returns:
            是否有效
        """
        # 基本检查：动作索引是否在映射中
        if action_idx not in action_mapping:
            return False
            
        # 检查动作索引范围
        if action_idx < 0 or action_idx >= self.max_actions:
            return False
            
        # 检查映射内容有效性
        target_type, target_idx = action_mapping[action_idx]
        if target_type not in ['local', 'satellite', 'cloud', 'drop']:
            return False
            
        return True
    
    def validate_task_atomicity(self, task: Any) -> bool:
        """
        验证任务的原子性约束
        
        Args:
            task: 任务对象
            
        Returns:
            是否满足原子性约束
        """
        # 检查任务状态
        valid_states = ['queued', 'processing', 'completed', 'dropped', 'migrating', 'offloading']
        if hasattr(task, 'status') and task.status not in valid_states:
            return False
        
        # 确保任务没有被分割
        if hasattr(task, 'is_split') and task.is_split:
            return False
        
        # 确保没有部分进度（任务要么未开始，要么已完成）
        if hasattr(task, 'partial_progress'):
            return False
        
        return True
    
    def _validate_target_availability(
        self,
        target_type: str,
        target_idx: int,
        compute_manager: Any
    ) -> bool:
        """
        验证目标是否可用
        
        Args:
            target_type: 目标类型
            target_idx: 目标索引
            compute_manager: 计算管理器
            
        Returns:
            目标是否可用
        """
        if target_type == 'local':
            return True  # 本地处理始终可用
            
        elif target_type == 'satellite':
            # 检查目标卫星是否存在且有空间
            if target_idx < 0 or target_idx >= 72:
                return False
            # 可以进一步检查目标卫星的队列容量
            return True
            
        elif target_type == 'cloud':
            # 检查云中心是否存在且可访问
            if target_idx < 0 or target_idx >= 5:
                return False
            return True
            
        elif target_type == 'drop':
            return True  # 丢弃始终可用
            
        return False
    
    def _handle_invalid_action(
        self,
        task: Any,
        satellite_idx: int,
        compute_manager: Any,
        reason: str
    ):
        """
        处理无效动作，降级为本地处理
        
        Args:
            task: 任务对象
            satellite_idx: 卫星索引
            compute_manager: 计算管理器
            reason: 失败原因
        """
        # 记录失败原因
        if hasattr(task, 'action_failures'):
            task.action_failures.append(reason)
        else:
            task.action_failures = [reason]
        
        # 降级为本地处理
        self._process_locally(
            task,
            satellite_idx,
            0.1,  # 默认低CPU分配
            compute_manager
        )
    
    def batch_process(
        self,
        actions: Dict[str, Dict],
        action_mappings: Dict[str, Dict],
        compute_manager: Any,
        timeslot: int
    ):
        """
        批量处理所有卫星的动作
        
        Args:
            actions: 所有智能体的动作
            action_mappings: 所有智能体的动作映射
            compute_manager: 计算管理器
            timeslot: 当前时隙
        """
        for agent_id, action in actions.items():
            satellite_idx = int(agent_id.split('_')[1])
            action_mapping = action_mappings.get(agent_id, {})
            
            self.process(
                satellite_idx,
                action,
                action_mapping,
                compute_manager,
                timeslot
            )