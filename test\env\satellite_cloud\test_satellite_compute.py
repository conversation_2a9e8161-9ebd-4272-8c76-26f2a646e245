"""
Test module for satellite compute resource management
Tests DPSQ scheduling algorithm and task processing capabilities
"""

import sys
import os
import yaml
import numpy as np
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.env.satellite_cloud.satellite_compute import SatelliteCompute, DPSQScheduler
from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus
from src.env.physics_layer.task_models import Task, TaskType
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config


class TestSatelliteCompute:
    """Test class for SatelliteCompute functionality"""
    
    def __init__(self, config_path: str = "src/env/physics_layer/config.yaml"):
        """Initialize test environment"""
        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize logging
        initialize_logging_and_config(config_path)
        
        # Create satellite instance
        self.satellite = SatelliteCompute(
            satellite_id=1,
            config=self.config,
            enable_gpu=False  # Disable GPU for testing
        )
        
        # Test parameters
        self.test_results = {
            'scenario1': {},
            'scenario2': {},
            'scenario4': {}
        }
    
    def create_test_tasks(self) -> Dict[str, Task]:
        """
        Create test tasks for different scenarios
        
        Returns:
            Dictionary of test tasks
        """
        tasks = {}
        
        # Scenario 1: Basic function test tasks
        # Small task (10MB, low complexity)
        tasks['small'] = Task(
            task_id=1,
            type_id=TaskType.REALTIME,
            data_size_mb=10.0,
            complexity_cycles_per_bit=100,  # Total complexity: 10*8*1e6*100 = 8e9 cycles
            deadline_timestamp=10.0,  # 10 seconds deadline
            priority=1,  # High priority
            location_id=1,
            coordinates=(0.0, 0.0),
            generation_time=0.0
        )
        
        # Medium task (50MB, medium complexity)
        tasks['medium'] = Task(
            task_id=2,
            type_id=TaskType.NORMAL,
            data_size_mb=50.0,
            complexity_cycles_per_bit=200,  # Total complexity: 50*8*1e6*200 = 8e10 cycles
            deadline_timestamp=30.0,  # 30 seconds deadline
            priority=2,  # Medium priority
            location_id=2,
            coordinates=(10.0, 10.0),
            generation_time=0.0
        )
        
        # Large task (200MB, high complexity)
        tasks['large'] = Task(
            task_id=3,
            type_id=TaskType.COMPUTE_INTENSIVE,
            data_size_mb=200.0,
            complexity_cycles_per_bit=500,  # Total complexity: 200*8*1e6*500 = 8e11 cycles
            deadline_timestamp=100.0,  # 100 seconds deadline
            priority=3,  # Low priority
            location_id=3,
            coordinates=(20.0, 20.0),
            generation_time=0.0
        )
        
        # Scenario 2: Concurrent processing test tasks
        for i in range(10):
            if i < 5:
                # High priority small tasks
                tasks[f'high_{i}'] = Task(
                    task_id=10 + i,
                    type_id=TaskType.REALTIME,
                    data_size_mb=5.0,
                    complexity_cycles_per_bit=50,
                    deadline_timestamp=20.0,
                    priority=1,
                    location_id=10 + i,
                    coordinates=(float(i), float(i)),
                    generation_time=0.0
                )
            elif i < 8:
                # Medium priority medium tasks
                tasks[f'med_{i}'] = Task(
                    task_id=10 + i,
                    type_id=TaskType.NORMAL,
                    data_size_mb=25.0,
                    complexity_cycles_per_bit=150,
                    deadline_timestamp=40.0,
                    priority=2,
                    location_id=10 + i,
                    coordinates=(float(i), float(i)),
                    generation_time=0.0
                )
            else:
                # Low priority large tasks
                tasks[f'low_{i}'] = Task(
                    task_id=10 + i,
                    type_id=TaskType.COMPUTE_INTENSIVE,
                    data_size_mb=100.0,
                    complexity_cycles_per_bit=300,
                    deadline_timestamp=60.0,
                    priority=3,
                    location_id=10 + i,
                    coordinates=(float(i), float(i)),
                    generation_time=0.0
                )
        
        # Scenario 4: Exception handling test tasks
        # Timeout task (very short deadline)
        tasks['timeout'] = Task(
            task_id=100,
            type_id=TaskType.REALTIME,
            data_size_mb=50.0,
            complexity_cycles_per_bit=1000,
            deadline_timestamp=0.001,  # 1ms deadline (impossible to meet)
            priority=1,
            location_id=100,
            coordinates=(0.0, 0.0),
            generation_time=0.0
        )
        
        # Zero complexity task
        tasks['zero_complexity'] = Task(
            task_id=101,
            type_id=TaskType.NORMAL,
            data_size_mb=10.0,
            complexity_cycles_per_bit=0,  # Zero complexity
            deadline_timestamp=10.0,
            priority=2,
            location_id=101,
            coordinates=(0.0, 0.0),
            generation_time=0.0
        )
        
        return tasks
    
    def test_scenario1_basic_functions(self):
        """Test Scenario 1: Basic function test"""
        print("\n" + "="*80)
        print("TEST SCENARIO 1: Basic Function Test")
        print("="*80)
        
        # Reset satellite
        self.satellite.reset()
        tasks = self.create_test_tasks()
        
        # Add three basic tasks
        print("\n>>> Adding test tasks...")
        for task_name in ['small', 'medium', 'large']:
            task = tasks[task_name]
            success = self.satellite.add_task(task)
            print(f"  Task {task_name} (ID={task.task_id}): {'[OK] Added' if success else '[FAIL] Failed'}")
        
        # Check initial queue status
        queue_status = self.satellite.get_queue_status()
        print(f"\nInitial Queue Status:")
        print(f"  Queue length: {queue_status['queue_length']}")
        print(f"  Processing count: {queue_status['processing_count']}")
        print(f"  Battery: {queue_status['energy_percentage']:.1f}%")
        
        # Process multiple timeslots
        print("\n>>> Processing timeslots...")
        timeslot_duration = 3.0  # 3 seconds per timeslot
        total_timeslots = 40  # Process 40 timeslots (120 seconds)
        
        for timeslot in range(total_timeslots):
            # Update current time
            self.satellite.current_time = timeslot * timeslot_duration
            
            # Process timeslot
            result = self.satellite.process_timeslot(
                duration=timeslot_duration,
                illuminated=(timeslot % 10 < 5)  # Alternate illumination
            )
            
            # Print status every 5 timeslots
            if timeslot % 5 == 0 or result.completed_tasks:
                print(f"\n  Timeslot {timeslot} (t={self.satellite.current_time:.1f}s):")
                status = self.satellite.get_queue_status()
                print(f"    Queue: {status['queue_length']}, Processing: {status['processing_count']}")
                print(f"    Processing IDs: {status['processing_task_ids']}")
                print(f"    Battery: {status['energy_percentage']:.1f}%")
                print(f"    CPU Utilization: {status['cpu_utilization']:.2%}")
                
                if result.completed_tasks:
                    print(f"    Completed tasks this timeslot:")
                    for task in result.completed_tasks:
                        print(f"      - Task {task.task_id}: Progress={task.processing_progress:.1%}")
        
        # Final statistics
        stats = self.satellite.get_statistics()
        print(f"\n>>> Final Statistics:")
        print(f"  Total processed: {stats['total_tasks_processed']}")
        print(f"  Total dropped: {stats['total_tasks_dropped']}")
        print(f"  Total energy consumed: {stats['total_energy_consumed']/1e6:.2f} MJ")
        print(f"  Completed tasks: {stats['completed_tasks']}")
        print(f"  Dropped tasks: {stats['dropped_tasks']}")
        
        # Validate results
        print(f"\n>>> Validation:")
        print(f"  [Test] Task A (small) should complete in 1-2 timeslots: ", end="")
        small_completed = any(t.task_id == 1 for t in self.satellite.completed_tasks)
        print("PASS" if small_completed else "FAIL")
        
        print(f"  [Test] Task B (medium) should complete in ~10 timeslots: ", end="")
        medium_completed = any(t.task_id == 2 for t in self.satellite.completed_tasks)
        print("PASS" if medium_completed else "FAIL")
        
        print(f"  [Test] Task C (large) should show partial processing: ", end="")
        large_task = next((t for t in self.satellite.processing_tasks + self.satellite.completed_tasks 
                          if t.task_id == 3), None)
        if large_task:
            print(f"PASS (Progress: {large_task.processing_progress:.1%})")
        else:
            print("FAIL")
        
        self.test_results['scenario1'] = {
            'small_completed': small_completed,
            'medium_completed': medium_completed,
            'large_progress': large_task.processing_progress if large_task else 0
        }
    
    def test_scenario2_concurrent_processing(self):
        """Test Scenario 2: Concurrent processing test"""
        print("\n" + "="*80)
        print("TEST SCENARIO 2: Concurrent Processing Test")
        print("="*80)
        
        # Reset satellite
        self.satellite.reset()
        tasks = self.create_test_tasks()
        
        # Add 10 concurrent tasks
        print("\n>>> Adding 10 concurrent tasks...")
        task_list = []
        for i in range(10):
            if i < 5:
                task = tasks[f'high_{i}']
                task_type = "High priority"
            elif i < 8:
                task = tasks[f'med_{i}']
                task_type = "Medium priority"
            else:
                task = tasks[f'low_{i}']
                task_type = "Low priority"
            
            success = self.satellite.add_task(task)
            task_list.append(task)
            print(f"  Task {task.task_id}: {task_type} - {'[OK]' if success else '[FAIL]'}")
        
        # Check initial status
        queue_status = self.satellite.get_queue_status()
        print(f"\nInitial Status:")
        print(f"  Total tasks in queue: {queue_status['queue_length']}")
        
        # Process timeslots and track priority order
        print("\n>>> Processing tasks...")
        timeslot_duration = 3.0
        processing_order = []
        
        for timeslot in range(30):  # Process 30 timeslots
            self.satellite.current_time = timeslot * timeslot_duration
            
            result = self.satellite.process_timeslot(
                duration=timeslot_duration,
                illuminated=True
            )
            
            # Track processing order
            if result.completed_tasks:
                for task in result.completed_tasks:
                    processing_order.append((task.task_id, task.priority))
                    print(f"  Timeslot {timeslot}: Completed Task {task.task_id} (Priority {task.priority})")
            
            # Show parallel processing status
            if timeslot % 5 == 0:
                status = self.satellite.get_queue_status()
                if status['processing_count'] > 0:
                    print(f"  Timeslot {timeslot}: Parallel processing {status['processing_count']} tasks")
                    print(f"    IDs: {status['processing_task_ids']}")
                    print(f"    CPU allocation: {1.0/max(1, status['processing_count']):.1%} per task")
        
        # Analyze priority order
        print("\n>>> Priority Analysis:")
        high_priority_tasks = [t for t in processing_order if t[1] == 1]
        medium_priority_tasks = [t for t in processing_order if t[1] == 2]
        low_priority_tasks = [t for t in processing_order if t[1] == 3]
        
        print(f"  High priority tasks completed: {len(high_priority_tasks)}/5")
        print(f"  Medium priority tasks completed: {len(medium_priority_tasks)}/3")
        print(f"  Low priority tasks completed: {len(low_priority_tasks)}/2")
        
        # Check if high priority tasks were processed first
        if processing_order:
            first_5_completed = processing_order[:5]
            high_priority_first = sum(1 for _, p in first_5_completed if p == 1)
            print(f"\n  High priority tasks in first 5 completed: {high_priority_first}/5")
            print(f"  {'[PASS]' if high_priority_first >= 3 else '[FAIL]'}: Priority ordering")
        
        # Check parallel processing
        max_parallel = max(len(self.satellite.processing_tasks) for _ in range(1))
        print(f"\n  Maximum parallel tasks observed: {max_parallel}")
        print(f"  {'[PASS]' if max_parallel > 1 else '[FAIL]'}: Parallel processing capability")
        
        self.test_results['scenario2'] = {
            'total_completed': len(processing_order),
            'high_priority_completed': len(high_priority_tasks),
            'priority_ordering': high_priority_first >= 3 if processing_order else False
        }
    
    def test_scenario4_exception_handling(self):
        """Test Scenario 4: Exception handling test"""
        print("\n" + "="*80)
        print("TEST SCENARIO 4: Exception Handling Test")
        print("="*80)
        
        # Reset satellite
        self.satellite.reset()
        tasks = self.create_test_tasks()
        
        print("\n>>> Test 1: Timeout Task")
        timeout_task = tasks['timeout']
        self.satellite.add_task(timeout_task)
        print(f"  Added task with 1ms deadline")
        
        # Process one timeslot
        self.satellite.current_time = 1.0  # Already past deadline
        result = self.satellite.process_timeslot(duration=3.0, illuminated=True)
        
        # Check if task was dropped
        dropped = any(t.task_id == timeout_task.task_id for t in self.satellite.dropped_tasks)
        print(f"  Task dropped due to timeout: {'[PASS]' if dropped else '[FAIL]'}")
        
        print("\n>>> Test 2: Queue Overflow")
        self.satellite.reset()
        max_queue = self.satellite.scheduler.max_queue_size
        print(f"  Max queue size: {max_queue}")
        
        # Try to add more tasks than queue capacity
        overflow_count = 0
        for i in range(max_queue + 50):
            task = Task(
                task_id=200 + i,
                type_id=TaskType.NORMAL,
                data_size_mb=10.0,
                complexity_cycles_per_bit=100,
                deadline_timestamp=100.0,
                priority=2,
                location_id=200 + i,
                coordinates=(0.0, 0.0),
                generation_time=0.0
            )
            if not self.satellite.add_task(task):
                overflow_count += 1
        
        print(f"  Tasks rejected due to queue overflow: {overflow_count}")
        print(f"  Queue size after overflow attempt: {len(self.satellite.task_queue)}")
        print(f"  {'[PASS]' if len(self.satellite.task_queue) == max_queue else '[FAIL]'}: Queue limit enforced")
        
        print("\n>>> Test 3: Zero Complexity Task")
        self.satellite.reset()
        zero_task = tasks['zero_complexity']
        self.satellite.add_task(zero_task)
        print(f"  Added task with zero complexity")
        
        # Process one timeslot
        self.satellite.current_time = 0.0
        result = self.satellite.process_timeslot(duration=3.0, illuminated=True)
        
        # Check if task was completed immediately
        completed = any(t.task_id == zero_task.task_id for t in self.satellite.completed_tasks)
        print(f"  Zero complexity task completed immediately: {'[PASS]' if completed else '[FAIL]'}")
        
        self.test_results['scenario4'] = {
            'timeout_handled': dropped,
            'queue_overflow_handled': len(self.satellite.task_queue) == max_queue,
            'zero_complexity_handled': completed
        }
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*80)
        print("TEST SUMMARY")
        print("="*80)
        
        all_pass = True
        
        # Scenario 1 results
        print("\nScenario 1 - Basic Functions:")
        s1 = self.test_results.get('scenario1', {})
        if s1:
            print(f"  Small task completed: {'[OK]' if s1.get('small_completed') else '[FAIL]'}")
            print(f"  Medium task completed: {'[OK]' if s1.get('medium_completed') else '[FAIL]'}")
            print(f"  Large task progress: {s1.get('large_progress', 0):.1%}")
            if not (s1.get('small_completed') and s1.get('medium_completed')):
                all_pass = False
        
        # Scenario 2 results
        print("\nScenario 2 - Concurrent Processing:")
        s2 = self.test_results.get('scenario2', {})
        if s2:
            print(f"  Total tasks completed: {s2.get('total_completed', 0)}")
            print(f"  High priority completed: {s2.get('high_priority_completed', 0)}/5")
            print(f"  Priority ordering: {'[OK]' if s2.get('priority_ordering') else '[FAIL]'}")
            if not s2.get('priority_ordering'):
                all_pass = False
        
        # Scenario 4 results
        print("\nScenario 4 - Exception Handling:")
        s4 = self.test_results.get('scenario4', {})
        if s4:
            print(f"  Timeout handling: {'[OK]' if s4.get('timeout_handled') else '[FAIL]'}")
            print(f"  Queue overflow: {'[OK]' if s4.get('queue_overflow_handled') else '[FAIL]'}")
            print(f"  Zero complexity: {'[OK]' if s4.get('zero_complexity_handled') else '[FAIL]'}")
            if not all([s4.get('timeout_handled'), s4.get('queue_overflow_handled'), 
                        s4.get('zero_complexity_handled')]):
                all_pass = False
        
        print("\n" + "="*80)
        if all_pass:
            print("OVERALL RESULT: [SUCCESS] ALL TESTS PASSED")
        else:
            print("OVERALL RESULT: [FAILURE] SOME TESTS FAILED")
        print("="*80)
    
    def run_all_tests(self):
        """Run all test scenarios"""
        print("\n" + "="*80)
        print("SATELLITE COMPUTE TEST SUITE")
        print("="*80)
        print("Testing satellite compute resource management with DPSQ algorithm")
        print(f"Configuration: {self.config['system']['num_leo_satellites']} satellites")
        print(f"CPU Frequency: {self.config['computation']['f_leo_hz']/1e12:.1f} THz")
        print(f"Battery Capacity: {self.config['computation']['leo_battery_capacity_j']/1e6:.1f} MJ")
        
        # Run each test scenario
        self.test_scenario1_basic_functions()
        self.test_scenario2_concurrent_processing()
        self.test_scenario4_exception_handling()
        
        # Print summary
        self.print_summary()


def main():
    """Main test execution"""
    # Create test instance
    tester = TestSatelliteCompute()
    
    # Run all tests
    tester.run_all_tests()


if __name__ == "__main__":
    main()