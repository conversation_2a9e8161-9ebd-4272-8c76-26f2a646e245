"""
Test simplified cloud compute (10x satellite)
"""

import sys
import os
import yaml
import numpy as np
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.env.satellite_cloud.cloud_compute_simplified import CloudCompute
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.env.physics_layer.task_models import Task, TaskType
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config


class SimplifiedCloudTest:
    """Test simplified cloud implementation"""
    
    def __init__(self, config_path: str = "src/env/physics_layer/config.yaml"):
        """Initialize test environment"""
        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize logging
        initialize_logging_and_config(config_path)
        
        print("\n" + "="*100)
        print("SIMPLIFIED CLOUD COMPUTE TEST (10x Satellite)")
        print("="*100)
    
    def test_cloud_vs_satellite(self):
        """Compare cloud vs satellite performance"""
        print("\n>>> TEST: Cloud vs Satellite Comparison")
        print("-" * 80)
        
        # Create cloud and satellite
        cloud = CloudCompute(cloud_id=1, config=self.config)
        satellite = SatelliteCompute(satellite_id=1, config=self.config, enable_gpu=False)
        
        # Compare specifications
        print("\n1. SPECIFICATIONS COMPARISON:")
        print(f"  Cloud CPU: {cloud.cpu_frequency/1e12:.2f} THz")
        print(f"  Satellite CPU: {satellite.cpu_frequency/1e12:.2f} THz")
        print(f"  Speed Ratio: {cloud.cpu_frequency/satellite.cpu_frequency:.1f}x")
        
        print(f"\n  Cloud Queue: {cloud.scheduler.max_queue_size}")
        print(f"  Satellite Queue: {satellite.scheduler.max_queue_size}")
        print(f"  Queue Ratio: {cloud.scheduler.max_queue_size/satellite.scheduler.max_queue_size:.1f}x")
        
        print(f"\n  Cloud Battery: {cloud.battery_energy/1e6:.0f} MJ (essentially unlimited)")
        print(f"  Satellite Battery: {satellite.battery_energy/1e6:.1f} MJ")
        
        # Create test task
        task = Task(
            task_id=1,
            type_id=TaskType.NORMAL,
            data_size_mb=50.0,
            complexity_cycles_per_bit=500,
            deadline_timestamp=100.0,
            priority=2,
            location_id=1,
            coordinates=(0.0, 0.0),
            generation_time=0.0
        )
        
        complexity = task.data_size_mb * 8e6 * task.complexity_cycles_per_bit
        print(f"\n2. TEST TASK:")
        print(f"  Data Size: {task.data_size_mb} MB")
        print(f"  Complexity: {complexity:.2e} cycles")
        
        # Test cloud processing
        print(f"\n3. CLOUD PROCESSING:")
        cloud.reset()
        cloud.add_task(task)
        
        timeslots = 0
        while cloud.get_queue_length() > 0 or len(cloud.processing_tasks) > 0:
            timeslots += 1
            cloud.current_time = timeslots * 3.0
            result = cloud.process_timeslot(duration=3.0, illuminated=False)  # No light needed
            
            if result.completed_tasks:
                print(f"  Completed in {timeslots} timeslots ({timeslots*3}s)")
                print(f"  Energy consumed: {result.energy_consumed:.2e} J")
                break
            
            if timeslots > 50:  # Safety limit
                print(f"  Failed to complete in 50 timeslots")
                break
        
        # Test satellite processing
        print(f"\n4. SATELLITE PROCESSING:")
        satellite.reset()
        satellite.add_task(task)
        
        sat_timeslots = 0
        while satellite.get_queue_length() > 0 or len(satellite.processing_tasks) > 0:
            sat_timeslots += 1
            satellite.current_time = sat_timeslots * 3.0
            result = satellite.process_timeslot(duration=3.0, illuminated=True)  # Needs light
            
            if result.completed_tasks:
                print(f"  Completed in {sat_timeslots} timeslots ({sat_timeslots*3}s)")
                print(f"  Energy consumed: {result.energy_consumed:.2e} J")
                break
            
            if sat_timeslots > 50:  # Safety limit
                print(f"  Failed to complete in 50 timeslots")
                break
        
        # Compare results
        print(f"\n5. PERFORMANCE COMPARISON:")
        if timeslots > 0 and sat_timeslots > 0:
            speedup = sat_timeslots / timeslots
            print(f"  Cloud: {timeslots} timeslots")
            print(f"  Satellite: {sat_timeslots} timeslots")
            print(f"  Actual Speedup: {speedup:.1f}x")
            print(f"  Expected Speedup: ~10x")
            
            if 8 <= speedup <= 12:  # Allow some variance
                print(f"  Result: [PASS] Speedup is approximately 10x")
            else:
                print(f"  Result: [FAIL] Speedup should be ~10x")
    
    def test_energy_constraints(self):
        """Test that cloud has no energy constraints"""
        print("\n>>> TEST: Energy Constraints")
        print("-" * 80)
        
        cloud = CloudCompute(cloud_id=1, config=self.config)
        
        # Create many large tasks
        print("\n1. Adding 100 large tasks to cloud:")
        for i in range(100):
            task = Task(
                task_id=100+i,
                type_id=TaskType.COMPUTE_INTENSIVE,
                data_size_mb=100.0,
                complexity_cycles_per_bit=1000,
                deadline_timestamp=1000.0,
                priority=2,
                location_id=i,
                coordinates=(0.0, 0.0),
                generation_time=0.0
            )
            success = cloud.add_task(task)
            if not success:
                print(f"  Failed to add task {i+1}")
                break
        
        print(f"  Successfully added {len(cloud.task_queue)} tasks")
        
        # Process for many timeslots
        print("\n2. Processing tasks continuously:")
        initial_battery = cloud.battery_energy
        
        for timeslot in range(10):
            cloud.current_time = timeslot * 3.0
            result = cloud.process_timeslot(duration=3.0, illuminated=False)  # No light
            
            if timeslot % 3 == 0:
                status = cloud.get_energy_status()
                print(f"  Timeslot {timeslot}: Battery at {status['energy_percentage']:.1f}%")
        
        # Check battery hasn't depleted
        final_battery = cloud.battery_energy
        print(f"\n3. Energy Status:")
        print(f"  Initial Battery: {initial_battery/1e6:.0f} MJ")
        print(f"  Final Battery: {final_battery/1e6:.0f} MJ")
        print(f"  Battery Depleted: {initial_battery > final_battery}")
        
        if cloud.battery_energy > cloud.max_battery * 0.99:
            print(f"  Result: [PASS] Cloud maintains unlimited energy")
        else:
            print(f"  Result: [FAIL] Cloud energy should not deplete")
    
    def test_parallel_processing(self):
        """Test cloud can handle many parallel tasks"""
        print("\n>>> TEST: Parallel Processing Capacity")
        print("-" * 80)
        
        cloud = CloudCompute(cloud_id=1, config=self.config)
        satellite = SatelliteCompute(satellite_id=1, config=self.config, enable_gpu=False)
        
        # Add many small tasks
        num_tasks = 200
        print(f"\n1. Adding {num_tasks} small tasks:")
        
        for i in range(num_tasks):
            task = Task(
                task_id=1000+i,
                type_id=TaskType.REALTIME,
                data_size_mb=1.0,
                complexity_cycles_per_bit=10,
                deadline_timestamp=100.0,
                priority=1,
                location_id=i,
                coordinates=(0.0, 0.0),
                generation_time=0.0
            )
            cloud.add_task(task)
            if i < 100:  # Satellite can only hold 100
                satellite.add_task(task)
        
        print(f"  Cloud queue: {len(cloud.task_queue)} tasks")
        print(f"  Satellite queue: {len(satellite.task_queue)} tasks (limited to 100)")
        
        # Process first timeslot
        cloud.current_time = 0.0
        satellite.current_time = 0.0
        
        cloud_result = cloud.process_timeslot(duration=3.0, illuminated=True)
        sat_result = satellite.process_timeslot(duration=3.0, illuminated=True)
        
        cloud_status = cloud.get_queue_status()
        sat_status = satellite.get_queue_status()
        
        print(f"\n2. Parallel Processing Status:")
        print(f"  Cloud processing: {cloud_status['processing_count']} tasks")
        print(f"  Satellite processing: {sat_status['processing_count']} tasks")
        
        if cloud_status['processing_count'] > sat_status['processing_count']:
            print(f"  Result: [PASS] Cloud processes more tasks in parallel")
        else:
            print(f"  Result: [FAIL] Cloud should process more tasks in parallel")
    
    def run_all_tests(self):
        """Run all tests"""
        self.test_cloud_vs_satellite()
        self.test_energy_constraints()
        self.test_parallel_processing()
        
        print("\n" + "="*100)
        print("TEST SUMMARY")
        print("="*100)
        print("\nKey Findings:")
        print("  1. Cloud is implemented as a 10x powerful satellite")
        print("  2. Cloud has 10x computing power of satellite")
        print("  3. Cloud has no energy constraints (unlimited battery)")
        print("  4. Cloud has 10x queue capacity")
        print("  5. Cloud can process more tasks in parallel")
        print("\nConclusion: Simplified cloud implementation works correctly!")


def main():
    """Main test execution"""
    tester = SimplifiedCloudTest()
    tester.run_all_tests()


if __name__ == "__main__":
    main()