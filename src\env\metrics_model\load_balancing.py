"""
负载均衡模块 (Load Balancing Module)

实现区域负载均衡计算，基于24个全球区域对LEO卫星的负载分布进行分析。
每个时隙计算各区域内卫星负载的方差，用于评估负载均衡程度。

核心公式:
- 单星负载: L_s(t) = w_cpu * U_cpu,s(t) + w_q * N_queue,s(t)/N_max
- 区域负载方差: B_k(t) = (1/|S_k(t)|) * Σ(L_s(t) - L̄_k(t))²

Author: SPACE2 Team
Version: 1.0
"""

import json
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

from src.env.Foundation_Layer.time_manager import TimeContext
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.logging_config import get_logger


@dataclass
class RegionalLoadResult:
    """区域负载计算结果"""
    timeslot: int
    regional_load_variances: Dict[int, float]  # 区域ID -> 负载方差
    regional_mean_loads: Dict[int, float]      # 区域ID -> 平均负载
    regional_satellite_counts: Dict[int, int]  # 区域ID -> 卫星数量
    total_regions_active: int                   # 活跃区域数量


@dataclass  
class SatelliteLoadInfo:
    """卫星负载信息"""
    satellite_id: int
    region_id: int
    cpu_utilization: float      # CPU利用率 (0-1)
    queue_length: int          # 任务队列长度
    load_score: float          # 综合负载评分
    latitude: float            # 纬度
    longitude: float           # 经度


class LoadBalancingCalculator:
    """负载均衡计算器"""
    
    def __init__(self, config: Dict[str, Any], regions_file: str = "src/env/env_data/regions.json"):
        """
        初始化负载均衡计算器
        
        Args:
            config: 系统配置字典
            regions_file: 区域定义文件路径
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 负载计算参数
        self.w_cpu = config.get('load_balancing', {}).get('w_cpu', 0.7)
        self.w_queue = config.get('load_balancing', {}).get('w_queue', 0.3)
        self.max_queue_size = config.get('queuing', {}).get('max_queue_size', 100)
        
        # 加载区域定义
        self.regions = self._load_regions(regions_file)
        
        # 一次性加载和缓存卫星数据
        self.satellite_data_cache = self._load_satellite_data()
        
        self.logger.info(f"负载均衡计算器初始化完成，加载{len(self.regions)}个区域定义")
    
    def _load_regions(self, regions_file: str) -> List[Dict]:
        """加载区域定义文件"""
        try:
            with open(regions_file, 'r', encoding='utf-8') as f:
                regions = json.load(f)
            self.logger.debug(f"成功加载{len(regions)}个区域定义")
            return regions
        except Exception as e:
            error_msg = f"加载区域定义文件失败: {regions_file}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise SpaceSimulationError(
                message=error_msg,
                error_code="REGIONS_LOAD_FAILURE"
            )
    
    def _load_satellite_data(self) -> Optional[Any]:
        """一次性加载卫星数据并缓存"""
        try:
            import pandas as pd
            satellite_data = pd.read_csv("src/env/env_data/satellite_data_72_0.csv", engine='python')
            self.logger.info(f"成功缓存{len(satellite_data)}条卫星位置数据")
            return satellite_data
        except Exception as e:
            self.logger.error(f"加载卫星数据失败: {e}")
            return None
    
    @handle_errors(module="load_balancing", function="determine_satellite_region")
    def _determine_satellite_region(self, latitude: float, longitude: float) -> Optional[int]:
        """
        根据卫星位置确定其所属区域
        
        Args:
            latitude: 纬度 (-90 to 90)
            longitude: 经度 (-180 to 180)
            
        Returns:
            区域ID，如果不在任何区域内则返回None
        """
        for region in self.regions:
            lat_range = region['latitude_range']
            lon_range = region['longitude_range']
            
            if (lat_range['min'] <= latitude <= lat_range['max'] and
                lon_range['min'] <= longitude <= lon_range['max']):
                return region['region_id']
        
        # 如果没有找到匹配的区域，记录警告
        self.logger.warning(f"卫星位置({latitude:.3f}, {longitude:.3f})不在任何定义区域内")
        return None
    
    def _group_satellites_by_region(self, satellites_data: List[Dict]) -> Dict[int, List[SatelliteLoadInfo]]:
        """
        按区域对卫星进行分组
        
        Args:
            satellites_data: 卫星数据列表，包含位置和负载信息
            
        Returns:
            区域ID -> 卫星负载信息列表的字典
        """
        regional_satellites = {}
        
        for sat_data in satellites_data:
            # 确定卫星所属区域
            region_id = self._determine_satellite_region(
                sat_data['latitude'], 
                sat_data['longitude']
            )
            
            if region_id is None:
                continue
                
            # 计算卫星负载评分
            load_score = (self.w_cpu * sat_data['cpu_utilization'] + 
                         self.w_queue * sat_data['queue_length'] / self.max_queue_size)
            
            # 创建卫星负载信息对象
            sat_load_info = SatelliteLoadInfo(
                satellite_id=sat_data['satellite_id'],
                region_id=region_id,
                cpu_utilization=sat_data['cpu_utilization'],
                queue_length=sat_data['queue_length'],
                load_score=load_score,
                latitude=sat_data['latitude'],
                longitude=sat_data['longitude']
            )
            
            # 添加到对应区域
            if region_id not in regional_satellites:
                regional_satellites[region_id] = []
            regional_satellites[region_id].append(sat_load_info)
        
        return regional_satellites
    
    @handle_errors(module="load_balancing", function="calculate_regional_load_variance")
    def _calculate_regional_load_variance(self, satellites_in_region: List[SatelliteLoadInfo]) -> Tuple[float, float]:
        """
        计算单个区域内的负载方差
        
        Args:
            satellites_in_region: 区域内卫星负载信息列表
            
        Returns:
            (负载方差, 平均负载)
        """
        if not satellites_in_region:
            return 0.0, 0.0
        
        # 提取所有卫星的负载评分
        loads = [sat.load_score for sat in satellites_in_region]
        
        # 计算平均负载
        mean_load = np.mean(loads)
        
        # 计算负载方差 B_k(t)
        if len(loads) == 1:
            variance = 0.0  # 只有一颗卫星时方差为0
        else:
            variance = np.var(loads, ddof=0)  # 使用总体方差公式
        
        return variance, mean_load
    
    @handle_errors(module="load_balancing", function="calculate_load_balance")
    def calculate_load_balance(self, timeslot: int, satellites_data: List[Dict]) -> RegionalLoadResult:
        """
        计算指定时隙的区域负载均衡结果
        
        Args:
            timeslot: 时隙编号
            satellites_data: 卫星数据列表，每个元素包含:
                - satellite_id: 卫星ID
                - latitude: 纬度
                - longitude: 经度  
                - cpu_utilization: CPU利用率 (0-1)
                - queue_length: 任务队列长度
                
        Returns:
            RegionalLoadResult: 区域负载计算结果
        """
        self.logger.debug(f"开始计算时隙{timeslot}的区域负载均衡")
        
        # 按区域分组卫星
        regional_satellites = self._group_satellites_by_region(satellites_data)
        
        # 计算各区域的负载方差
        regional_variances = {}
        regional_means = {}
        regional_counts = {}
        
        for region_id, satellites_in_region in regional_satellites.items():
            variance, mean_load = self._calculate_regional_load_variance(satellites_in_region)
            
            regional_variances[region_id] = variance
            regional_means[region_id] = mean_load
            regional_counts[region_id] = len(satellites_in_region)
            
            self.logger.debug(
                f"区域{region_id}: {len(satellites_in_region)}颗卫星, "
                f"平均负载={mean_load:.4f}, 方差={variance:.6f}"
            )
        
        # 创建结果对象
        result = RegionalLoadResult(
            timeslot=timeslot,
            regional_load_variances=regional_variances,
            regional_mean_loads=regional_means,
            regional_satellite_counts=regional_counts,
            total_regions_active=len(regional_variances)
        )
        
        self.logger.info(
            f"时隙{timeslot}负载均衡计算完成: "
            f"{result.total_regions_active}个活跃区域, "
            f"平均方差={np.mean(list(regional_variances.values())):.6f}"
        )
        
        return result
    
    def get_simple_regional_loads(self, satellites_compute: List, timeslot: int = 0) -> Dict:
        """
        简单方法获取区域负载信息，供PettingZoo环境使用
        
        Args:
            satellites_compute: 卫星计算组件列表
            timeslot: 时隙
            
        Returns:
            区域负载字典
        """
        # 简单的区域负载计算
        regional_loads = {}
        
        # 计算所有卫星的平均指标
        cpu_utils = []
        queue_lens = []
        
        for sat_compute in satellites_compute:
            # 使用正确的接口获取队列状态
            queue_status = sat_compute.get_queue_status()
            cpu_utils.append(queue_status.get('cpu_utilization', 0.3))
            queue_lens.append(queue_status.get('queue_length', 0))
        
        # 为简化，返回一个全局的负载信息（不分区域）
        regional_loads[0] = {
            'mean_cpu_utilization': np.mean(cpu_utils) if cpu_utils else 0.3,
            'mean_queue_length': np.mean(queue_lens) if queue_lens else 0.0,
            'load_variance': np.var(cpu_utils) if cpu_utils else 0.0,
            'total_tasks': sum(queue_lens)
        }
        
        return regional_loads
    
    def get_satellite_region(self, satellite_idx: int, timeslot: int = 0) -> int:
        """
        根据卫星索引获取其所属区域ID（使用真实地理区域）
        
        Args:
            satellite_idx: 卫星索引 (0-71)
            timeslot: 时隙（用于获取当前位置）
            
        Returns:
            区域ID (1-24)
        """
        # 使用缓存数据
        if self.satellite_data_cache is not None:
            try:
                # 卫星ID现在直接使用索引值 (0-71)
                satellite_id_num = satellite_idx  # 卫星ID直接对应索引
                sat_data = self.satellite_data_cache[
                    (self.satellite_data_cache['卫星ID'] == satellite_id_num) & 
                    (self.satellite_data_cache['时隙'] == timeslot)
                ]
                
                if not sat_data.empty:
                    # 获取卫星当前位置
                    latitude = sat_data.iloc[0]['纬度']
                    longitude = sat_data.iloc[0]['经度']
                    
                    # 使用现有的区域判断方法
                    region_id = self._determine_satellite_region(latitude, longitude)
                    return region_id if region_id is not None else 1
                
            except Exception as e:
                self.logger.warning(f"从缓存读取卫星{satellite_idx}在时隙{timeslot}的位置失败: {e}")
        
        # 备用方案：基于24个区域的简单划分
        return (satellite_idx % 24) + 1
    
    def get_region_satellites(self, region_id: int, timeslot: int = 0) -> List[int]:
        """
        获取指定区域内的所有卫星索引
        
        Args:
            region_id: 区域ID
            timeslot: 时隙
            
        Returns:
            卫星索引列表
        """
        satellites_in_region = []
        
        # 遍历所有卫星，找到属于该区域的
        for sat_idx in range(72):  # 72颗卫星
            if self.get_satellite_region(sat_idx, timeslot) == region_id:
                satellites_in_region.append(sat_idx)
                
        return satellites_in_region
    
    def get_load_balance_summary(self, result: RegionalLoadResult) -> Dict[str, Any]:
        """
        获取负载均衡结果摘要
        
        Args:
            result: 区域负载计算结果
            
        Returns:
            包含统计信息的字典
        """
        if not result.regional_load_variances:
            return {
                "timeslot": result.timeslot,
                "total_regions": 0,
                "average_variance": 0.0,
                "max_variance": 0.0,
                "min_variance": 0.0,
                "total_satellites": 0
            }
        
        variances = list(result.regional_load_variances.values())
        total_satellites = sum(result.regional_satellite_counts.values())
        
        return {
            "timeslot": result.timeslot,
            "total_regions": result.total_regions_active,
            "average_variance": np.mean(variances),
            "max_variance": np.max(variances),
            "min_variance": np.min(variances),
            "variance_std": np.std(variances),
            "total_satellites": total_satellites,
            "most_unbalanced_region": max(result.regional_load_variances.items(), key=lambda x: x[1])[0],
            "most_balanced_region": min(result.regional_load_variances.items(), key=lambda x: x[1])[0]
        }
    
    def get_regional_load_balance_values(self, satellites_compute: List, timeslot: int = 0, 
                                       orbital_updater=None) -> Dict[int, float]:
        """
        获取所有区域的负载均衡数值
        
        Args:
            satellites_compute: 卫星计算组件列表
            timeslot: 时隙编号
            orbital_updater: 轨道更新器（用于获取卫星位置）
            
        Returns:
            区域ID -> 负载均衡指数的字典，负载均衡指数越高表示该区域越均衡（0-1区间）
        """
        # 构建包含位置和负载信息的卫星数据
        satellites_data = self._build_satellite_data_with_positions(
            satellites_compute, timeslot, orbital_updater
        )
        
        if not satellites_data:
            # 如果无法获取位置数据，使用简化方法
            return self._get_simplified_regional_load_balance(satellites_compute)
        
        # 使用完整的区域负载均衡计算
        result = self.calculate_load_balance(timeslot, satellites_data)
        
        # 将方差转换为负载均衡指数：方差越小，均衡指数越高
        regional_balance_values = {}
        for region_id, variance in result.regional_load_variances.items():
            # 使用负指数函数将方差映射到0-1区间
            balance_index = np.exp(-variance * 5)  # 调整系数以控制敏感度
            regional_balance_values[region_id] = min(1.0, max(0.0, balance_index))
        
        return regional_balance_values
    
    def get_total_load_balance_sum(self, satellites_compute: List, timeslot: int = 0,
                                 orbital_updater=None) -> float:
        """
        获取所有区域负载均衡的求和
        
        Args:
            satellites_compute: 卫星计算组件列表
            timeslot: 时隙编号
            orbital_updater: 轨道更新器（用于获取卫星位置）
            
        Returns:
            所有区域负载均衡指数的总和
        """
        regional_values = self.get_regional_load_balance_values(
            satellites_compute, timeslot, orbital_updater
        )
        
        return sum(regional_values.values())
    
    def _build_satellite_data_with_positions(self, satellites_compute: List, timeslot: int,
                                           orbital_updater) -> List[Dict]:
        """
        构建包含位置和负载信息的卫星数据列表
        
        Args:
            satellites_compute: 卫星计算组件列表
            timeslot: 时隙编号
            orbital_updater: 轨道更新器
            
        Returns:
            卫星数据列表，每个元素包含satellite_id, latitude, longitude, cpu_utilization, queue_length
        """
        satellites_data = []
        
        try:
            # 如果有orbital_updater，获取卫星位置
            if orbital_updater:
                satellites_positions = orbital_updater.get_satellites_at_time(timeslot)
            else:
                satellites_positions = None
                
            for i, sat_compute in enumerate(satellites_compute):
                # 获取负载状态
                queue_status = sat_compute.get_queue_status()
                
                # 获取位置信息
                if satellites_positions:
                    sat_key = f'sat_{i:02d}'
                    if sat_key in satellites_positions:
                        satellite = satellites_positions[sat_key]
                        latitude = satellite.latitude
                        longitude = satellite.longitude
                    else:
                        # 尝试从缓存数据获取位置
                        latitude, longitude = self._get_satellite_position_from_cache(i, timeslot)
                else:
                    # 从缓存数据获取位置
                    latitude, longitude = self._get_satellite_position_from_cache(i, timeslot)
                
                if latitude is not None and longitude is not None:
                    satellites_data.append({
                        'satellite_id': i,
                        'latitude': latitude,
                        'longitude': longitude,
                        'cpu_utilization': queue_status.get('cpu_utilization', 0.3),
                        'queue_length': queue_status.get('queue_length', 0)
                    })
                    
        except Exception as e:
            self.logger.warning(f"构建卫星位置数据失败: {e}")
            return []
            
        return satellites_data
    
    def _get_satellite_position_from_cache(self, satellite_idx: int, timeslot: int) -> Tuple[Optional[float], Optional[float]]:
        """
        从缓存数据获取卫星位置
        
        Args:
            satellite_idx: 卫星索引 (0-71)
            timeslot: 时隙编号
            
        Returns:
            (latitude, longitude) 或 (None, None)
        """
        if self.satellite_data_cache is not None:
            try:
                # 卫星ID现在直接使用索引值 (0-71)
                satellite_id_num = satellite_idx  # 卫星ID直接对应索引
                sat_data = self.satellite_data_cache[
                    (self.satellite_data_cache['卫星ID'] == satellite_id_num) & 
                    (self.satellite_data_cache['时隙'] == timeslot)
                ]
                
                if not sat_data.empty:
                    return sat_data.iloc[0]['纬度'], sat_data.iloc[0]['经度']
                    
            except Exception as e:
                self.logger.warning(f"从缓存读取卫星{satellite_idx}在时隙{timeslot}的位置失败: {e}")
        
        return None, None
    
    def _get_simplified_regional_load_balance(self, satellites_compute: List) -> Dict[int, float]:
        """
        简化的区域负载均衡计算（当无法获取位置时使用）
        
        Args:
            satellites_compute: 卫星计算组件列表
            
        Returns:
            区域ID -> 负载均衡指数的字典
        """
        # 将72颗卫星平均分配到24个区域
        regional_balance = {}
        satellites_per_region = len(satellites_compute) // 24
        
        for region_id in range(1, 25):  # 24个区域
            start_idx = (region_id - 1) * satellites_per_region
            end_idx = min(start_idx + satellites_per_region, len(satellites_compute))
            
            if start_idx >= len(satellites_compute):
                regional_balance[region_id] = 1.0
                continue
                
            # 获取该区域卫星的负载数据
            region_cpu_utils = []
            for i in range(start_idx, end_idx):
                queue_status = satellites_compute[i].get_queue_status()
                region_cpu_utils.append(queue_status.get('cpu_utilization', 0.3))
            
            # 计算该区域的负载方差
            if len(region_cpu_utils) > 1:
                variance = np.var(region_cpu_utils)
                balance_index = np.exp(-variance * 10)
                regional_balance[region_id] = min(1.0, max(0.0, balance_index))
            else:
                regional_balance[region_id] = 1.0  # 单个卫星时认为完全均衡
                
        return regional_balance