"""
Task tracking module for SPACE2 simulation
Provides task tracking functionality for metrics analysis
"""

from dataclasses import dataclass
from typing import Optional
from enum import Enum

# Import the existing TaskStatus from compute_models
from src.env.satellite_cloud.compute_models import TaskStatus

# Import TaskType from task_models
from src.env.physics_layer.task_models import TaskType


@dataclass
class TaskTrackingRecord:
    """Record for tracking task lifecycle and metrics"""
    task_id: str
    task_type: TaskType
    status: TaskStatus
    arrival_time: float
    start_time: Optional[float] = None
    completion_time: Optional[float] = None
    assigned_satellite: Optional[int] = None
    data_size_mb: float = 0.0
    complexity: float = 0.0
    priority: int = 2
    source_location_id: int = 0
    target_node: Optional[str] = None
    
    @property
    def processing_time(self) -> Optional[float]:
        """Calculate processing time if available"""
        if self.start_time and self.completion_time:
            return self.completion_time - self.start_time
        return None
    
    @property
    def queuing_time(self) -> Optional[float]:
        """Calculate queuing time if available"""
        if self.start_time and self.arrival_time:
            return self.start_time - self.arrival_time
        return None
    
    @property
    def total_latency(self) -> Optional[float]:
        """Calculate total end-to-end latency"""
        if self.completion_time and self.arrival_time:
            return self.completion_time - self.arrival_time
        return None


# Re-export for compatibility
__all__ = ['TaskTrackingRecord', 'TaskStatus', 'TaskType']