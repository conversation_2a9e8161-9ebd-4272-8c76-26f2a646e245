"""
JSON到NPZ格式转换器 - 时隙分片存储方案
将integrated_data.json转换为高效的NumPy二进制格式
"""

import json
import numpy as np
import scipy.sparse as sp
from pathlib import Path
import time
from typing import Dict, List, Any, Tuple
import os
import sys

class DataConverter:
    def __init__(self, json_file: str = 'integrated_data.json', output_dir: str = 'src/data/npz_data'):
        """
        初始化数据转换器
        
        Args:
            json_file: 输入的JSON文件路径
            output_dir: 输出NPZ文件的目录
        """
        self.json_file = json_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建时隙目录
        self.timeslot_dir = self.output_dir / 'timeslots'
        self.timeslot_dir.mkdir(exist_ok=True)
        
        # 系统参数
        self.num_satellites = 72
        self.num_ground_stations = 420
        self.num_cloud_stations = 5
        
        # 数据类型定义
        self.task_dtype = np.dtype([
            ('task_id', 'u4'),           # 任务ID
            ('satellite_id', 'u1'),       # 分配的卫星ID (0-71)
            ('station_id', 'u2'),         # 地面站ID (0-419)
            ('data_size_mb', 'f4'),      # 数据大小
            ('complexity', 'f4'),         # 复杂度
            ('priority', 'u1'),           # 优先级 (1-3)
            ('deadline', 'f4'),           # 截止时间
            ('generation_time', 'u2')     # 生成时隙
        ])
        
        self.link_dtype = np.dtype([
            ('distance_km', 'f4'),
            ('rate_mbps', 'f4')
        ])
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'total_ground_links': 0,
            'total_cloud_links': 0,
            'total_isl_links': 0,
            'file_sizes': []
        }
    
    def load_json_data(self) -> List[Dict]:
        """加载JSON数据文件"""
        print(f"正在加载JSON文件: {self.json_file}")
        start_time = time.time()
        
        with open(self.json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        load_time = time.time() - start_time
        file_size_mb = os.path.getsize(self.json_file) / (1024 * 1024)
        print(f"  - 文件大小: {file_size_mb:.1f} MB")
        print(f"  - 加载时间: {load_time:.2f} 秒")
        print(f"  - 时隙数量: {len(data)}")
        
        return data
    
    def extract_satellite_states(self, satellites_data: Dict) -> np.ndarray:
        """
        提取卫星状态数组
        
        Returns:
            形状为 (72, 5) 的数组：[纬度, 经度, 光照, 状态, 区域ID]
        """
        states = np.zeros((self.num_satellites, 5), dtype=np.float32)
        
        for sat_id_str, sat_data in satellites_data.items():
            sat_id = int(sat_id_str)
            if sat_id < self.num_satellites:
                state = sat_data['state']
                states[sat_id] = [
                    state[0],  # 纬度
                    state[1],  # 经度
                    state[2],  # 光照 (0/1)
                    state[3],  # 状态 (0/1)
                    state[4] if state[4] is not None else 0  # 区域ID
                ]
        
        return states
    
    def extract_ground_links(self, satellites_data: Dict) -> Tuple[sp.csr_matrix, sp.csr_matrix, sp.csr_matrix]:
        """
        提取地面站链路信息（稀疏矩阵）
        
        Returns:
            三个稀疏矩阵：距离、上行速率、下行速率
        """
        # 准备数据列表
        rows, cols = [], []
        distances, uplinks, downlinks = [], [], []
        
        for sat_id_str, sat_data in satellites_data.items():
            sat_id = int(sat_id_str)
            if sat_id >= self.num_satellites:
                continue
                
            for station_info in sat_data.get('visible_ground_stations', []):
                station_id = station_info['station_id']
                
                rows.append(sat_id)
                cols.append(station_id)
                distances.append(station_info['distance_km'])
                uplinks.append(station_info['uplink_mbps'])
                downlinks.append(station_info['downlink_mbps'])
                
                self.stats['total_ground_links'] += 1
        
        # 创建稀疏矩阵
        shape = (self.num_satellites, self.num_ground_stations)
        
        if rows:  # 如果有数据
            distance_matrix = sp.csr_matrix(
                (distances, (rows, cols)), shape=shape, dtype=np.float32
            )
            uplink_matrix = sp.csr_matrix(
                (uplinks, (rows, cols)), shape=shape, dtype=np.float32
            )
            downlink_matrix = sp.csr_matrix(
                (downlinks, (rows, cols)), shape=shape, dtype=np.float32
            )
        else:  # 空矩阵
            distance_matrix = sp.csr_matrix(shape, dtype=np.float32)
            uplink_matrix = sp.csr_matrix(shape, dtype=np.float32)
            downlink_matrix = sp.csr_matrix(shape, dtype=np.float32)
        
        return distance_matrix, uplink_matrix, downlink_matrix
    
    def extract_cloud_links(self, satellites_data: Dict) -> Tuple[sp.csr_matrix, sp.csr_matrix, sp.csr_matrix]:
        """
        提取云站链路信息（稀疏矩阵）
        
        Returns:
            三个稀疏矩阵：距离、卫星到云速率、云到卫星速率
        """
        rows, cols = [], []
        distances, sat_to_cloud, cloud_to_sat = [], [], []
        
        # 云站ID映射
        cloud_id_map = {f'cloud_{i}': i for i in range(self.num_cloud_stations)}
        
        for sat_id_str, sat_data in satellites_data.items():
            sat_id = int(sat_id_str)
            if sat_id >= self.num_satellites:
                continue
                
            for cloud_info in sat_data.get('cloud_links', []):
                cloud_id_str = cloud_info['cloud_id']
                if cloud_id_str in cloud_id_map:
                    cloud_idx = cloud_id_map[cloud_id_str]
                    
                    rows.append(sat_id)
                    cols.append(cloud_idx)
                    distances.append(cloud_info['distance_km'])
                    sat_to_cloud.append(cloud_info['sat_to_cloud_mbps'])
                    cloud_to_sat.append(cloud_info['cloud_to_sat_mbps'])
                    
                    self.stats['total_cloud_links'] += 1
        
        # 创建稀疏矩阵
        shape = (self.num_satellites, self.num_cloud_stations)
        
        if rows:
            distance_matrix = sp.csr_matrix(
                (distances, (rows, cols)), shape=shape, dtype=np.float32
            )
            s2c_matrix = sp.csr_matrix(
                (sat_to_cloud, (rows, cols)), shape=shape, dtype=np.float32
            )
            c2s_matrix = sp.csr_matrix(
                (cloud_to_sat, (rows, cols)), shape=shape, dtype=np.float32
            )
        else:
            distance_matrix = sp.csr_matrix(shape, dtype=np.float32)
            s2c_matrix = sp.csr_matrix(shape, dtype=np.float32)
            c2s_matrix = sp.csr_matrix(shape, dtype=np.float32)
        
        return distance_matrix, s2c_matrix, c2s_matrix
    
    def extract_isl_links(self, satellites_data: Dict) -> sp.csr_matrix:
        """
        提取星间链路信息（稀疏邻接矩阵）
        
        Returns:
            稀疏矩阵：ISL距离矩阵
        """
        rows, cols, distances = [], [], []
        
        for sat_id_str, sat_data in satellites_data.items():
            sat_id = int(sat_id_str)
            if sat_id >= self.num_satellites:
                continue
                
            for isl_info in sat_data.get('isl_links', []):
                neighbor_id = isl_info['neighbor_id']
                if neighbor_id < self.num_satellites:
                    rows.append(sat_id)
                    cols.append(neighbor_id)
                    distances.append(isl_info['distance_km'])
                    
                    self.stats['total_isl_links'] += 1
        
        # 创建稀疏矩阵
        shape = (self.num_satellites, self.num_satellites)
        
        if rows:
            isl_matrix = sp.csr_matrix(
                (distances, (rows, cols)), shape=shape, dtype=np.float32
            )
        else:
            isl_matrix = sp.csr_matrix(shape, dtype=np.float32)
        
        return isl_matrix
    
    def extract_tasks(self, satellites_data: Dict, timeslot_id: int) -> np.ndarray:
        """
        提取任务数据
        
        Returns:
            结构化数组包含所有任务信息
        """
        tasks_list = []
        
        for sat_id_str, sat_data in satellites_data.items():
            sat_id = int(sat_id_str)
            if sat_id >= self.num_satellites:
                continue
                
            for station_info in sat_data.get('visible_ground_stations', []):
                station_id = station_info['station_id']
                
                for task in station_info.get('tasks', []):
                    task_entry = np.zeros(1, dtype=self.task_dtype)[0]
                    task_entry['task_id'] = task['task_id']
                    task_entry['satellite_id'] = sat_id
                    task_entry['station_id'] = station_id
                    task_entry['data_size_mb'] = task['data_size_mb']
                    task_entry['complexity'] = task['complexity']
                    task_entry['priority'] = task['priority']
                    task_entry['deadline'] = task['deadline']
                    task_entry['generation_time'] = task.get('generation_time', timeslot_id)
                    
                    tasks_list.append(task_entry)
                    self.stats['total_tasks'] += 1
        
        if tasks_list:
            return np.array(tasks_list, dtype=self.task_dtype)
        else:
            return np.array([], dtype=self.task_dtype)
    
    def convert_timeslot(self, timeslot_data: Dict) -> Dict[str, Any]:
        """
        转换单个时隙的数据
        
        Returns:
            包含所有转换后数组的字典
        """
        timeslot_id = timeslot_data['timeslot_id']
        satellites_data = timeslot_data['satellites']
        
        # 提取各种数据
        satellite_states = self.extract_satellite_states(satellites_data)
        
        # 地面链路
        ground_dist, ground_up, ground_down = self.extract_ground_links(satellites_data)
        
        # 云链路
        cloud_dist, cloud_s2c, cloud_c2s = self.extract_cloud_links(satellites_data)
        
        # ISL链路
        isl_matrix = self.extract_isl_links(satellites_data)
        
        # 任务数据
        tasks = self.extract_tasks(satellites_data, timeslot_id)
        
        return {
            'timeslot_id': timeslot_id,
            'satellite_states': satellite_states,
            'ground_distance': ground_dist,
            'ground_uplink': ground_up,
            'ground_downlink': ground_down,
            'cloud_distance': cloud_dist,
            'cloud_s2c': cloud_s2c,
            'cloud_c2s': cloud_c2s,
            'isl_distance': isl_matrix,
            'tasks': tasks
        }
    
    def save_timeslot_npz(self, timeslot_data: Dict[str, Any]):
        """保存单个时隙的NPZ文件"""
        timeslot_id = timeslot_data['timeslot_id']
        filename = self.timeslot_dir / f'ts_{timeslot_id:04d}.npz'
        
        # 保存为压缩的NPZ文件
        np.savez_compressed(
            filename,
            timeslot_id=timeslot_id,
            satellite_states=timeslot_data['satellite_states'],
            ground_distance=timeslot_data['ground_distance'],
            ground_uplink=timeslot_data['ground_uplink'],
            ground_downlink=timeslot_data['ground_downlink'],
            cloud_distance=timeslot_data['cloud_distance'],
            cloud_s2c=timeslot_data['cloud_s2c'],
            cloud_c2s=timeslot_data['cloud_c2s'],
            isl_distance=timeslot_data['isl_distance'],
            tasks=timeslot_data['tasks']
        )
        
        # 记录文件大小
        file_size = os.path.getsize(filename)
        self.stats['file_sizes'].append(file_size)
        
        return filename, file_size
    
    def save_metadata(self, num_timeslots: int):
        """保存元数据文件"""
        metadata = {
            'num_timeslots': num_timeslots,
            'num_satellites': self.num_satellites,
            'num_ground_stations': self.num_ground_stations,
            'num_cloud_stations': self.num_cloud_stations,
            'data_types': {
                'satellite_states': 'float32 (72, 5)',
                'ground_links': 'sparse csr_matrix float32',
                'cloud_links': 'sparse csr_matrix float32',
                'isl_links': 'sparse csr_matrix float32',
                'tasks': f'structured array with dtype: {str(self.task_dtype)}'
            },
            'statistics': {
                'total_tasks': self.stats['total_tasks'],
                'total_ground_links': self.stats['total_ground_links'],
                'total_cloud_links': self.stats['total_cloud_links'],
                'total_isl_links': self.stats['total_isl_links'],
                'avg_file_size_kb': np.mean(self.stats['file_sizes']) / 1024 if self.stats['file_sizes'] else 0,
                'total_size_mb': sum(self.stats['file_sizes']) / (1024 * 1024) if self.stats['file_sizes'] else 0
            }
        }
        
        metadata_file = self.output_dir / 'metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        return metadata
    
    def convert_all(self):
        """执行完整的转换流程"""
        print("\n" + "="*60)
        print("JSON到NPZ数据转换器 - 时隙分片存储方案")
        print("="*60)
        
        # 加载JSON数据
        json_data = self.load_json_data()
        num_timeslots = len(json_data)
        
        print(f"\n开始转换 {num_timeslots} 个时隙...")
        print("-" * 40)
        
        start_time = time.time()
        
        # 转换每个时隙
        for i, timeslot_json in enumerate(json_data):
            # 转换数据
            timeslot_npz = self.convert_timeslot(timeslot_json)
            
            # 保存NPZ文件
            filename, file_size = self.save_timeslot_npz(timeslot_npz)
            
            # 进度显示
            if (i + 1) % 100 == 0 or i == 0:
                progress = (i + 1) / num_timeslots * 100
                print(f"  进度: {i+1}/{num_timeslots} ({progress:.1f}%) - "
                      f"最新文件: {filename.name} ({file_size/1024:.1f} KB)")
        
        # 保存元数据
        metadata = self.save_metadata(num_timeslots)
        
        # 转换完成
        total_time = time.time() - start_time
        total_size_mb = sum(self.stats['file_sizes']) / (1024 * 1024)
        avg_size_kb = np.mean(self.stats['file_sizes']) / 1024
        
        print("\n" + "="*60)
        print("转换完成！")
        print("="*60)
        print(f"\n统计信息:")
        print(f"  - 转换时间: {total_time:.2f} 秒")
        print(f"  - 时隙数量: {num_timeslots}")
        print(f"  - NPZ文件总大小: {total_size_mb:.1f} MB")
        print(f"  - 平均文件大小: {avg_size_kb:.1f} KB/时隙")
        print(f"  - 压缩率: {total_size_mb / (os.path.getsize(self.json_file) / (1024*1024)) * 100:.1f}%")
        print(f"\n数据统计:")
        print(f"  - 总任务数: {self.stats['total_tasks']:,}")
        print(f"  - 总地面链路: {self.stats['total_ground_links']:,}")
        print(f"  - 总云链路: {self.stats['total_cloud_links']:,}")
        print(f"  - 总ISL链路: {self.stats['total_isl_links']:,}")
        print(f"\n输出目录: {self.output_dir.absolute()}")


def main():
    """主函数"""
    # 检查JSON文件是否存在
    json_file = 'integrated_data.json'
    if not os.path.exists(json_file):
        print(f"错误: 找不到输入文件 {json_file}")
        print("请先运行 integrate_data.py 生成JSON文件")
        sys.exit(1)
    
    # 创建转换器并执行转换
    converter = DataConverter(
        json_file=json_file,
        output_dir='src/data/npz_data'
    )
    converter.convert_all()


if __name__ == '__main__':
    main()