# SPACE2 PettingZoo环境开发文档（修订版）

## 第一部分：环境核心概念定义

### 1. 环境基本信息
- **环境名称**: `LEOSatelliteEdgeComputing`  
- **环境版本**: `v0`
- **环境类型**: Parallel (并行环境)
- **基于框架**: PettingZoo Parallel API

### 2. 核心玩法描述

SPACE2是一个基于Dec-POMDP建模的LEO卫星星座边缘计算多智能体环境。72个卫星智能体需要协同处理来自420个地面用户的动态计算任务。核心特点：
1. **动态动作空间**：基于实时可见性计算，动态确定可用的卸载目标
2. **不可分割任务**：每个任务必须完整地在一个位置（本地/邻居/云）处理完成
3. **与MAPPO算法集成**：环境提供动作空间信息，MAPPO使用Transformer策略网络生成动作

### 3. 智能体定义

- **数量**: 72个LEO卫星（基于config.yaml中的`num_leo_satellites`）
- **命名**: `satellite_0`, `satellite_1`, ..., `satellite_71`
- **目标**: 
  - 最大化任务完成率（优先级加权）
  - 最小化任务处理延迟
  - 优化能源消耗
  - 实现区域负载均衡

### 4. 任务模型

#### 4.1 任务原子性
- **不可分割原则**：每个任务是原子的，必须完整地在一个计算节点上处理
- **禁止任务分割**：任务不能被分割到多个节点并行处理
- **完整迁移**：如果任务需要迁移，必须将整个任务（包括所有数据和计算需求）迁移到目标节点

#### 4.2 任务处理流程
```python
# 任务只能有以下状态转换
task_states = {
    "queued": "任务在队列中等待",
    "processing": "任务正在某个节点完整处理",
    "completed": "任务处理完成",
    "dropped": "任务被丢弃"
}
# 不存在"partially_processed"状态
```

### 5. 动态动作空间设计

#### 5.1 动作空间组成

动作空间根据每个卫星的实时可见性动态确定，包含9个固定目标：

```python
# 动作空间组成（固定9个选项）
action_targets = {
    0: "本地处理",           # 始终可用
    1-6: "邻居卫星1-6",      # 固定6个邻居卫星位置
    7: "云中心",             # 基于可见性，最多1个
    8: "丢弃任务"           # 始终可用
}
```

#### 5.2 动态动作空间实现

```python
class DynamicActionSpace:
    """
    基于可见性的动态动作空间
    根据实时的卫星间、卫星-云中心可见性动态确定可用动作
    """
    
    def __init__(self, config):
        self.config = config
        self.num_neighbors = 6  # 固定选择6个邻居卫星
        self.num_cloud_centers = config['system']['num_cloud_centers']  # 5个云中心
        
    def compute_action_space(self, satellite_idx, visibility_matrices, distances):
        """
        计算特定卫星的动态动作空间
        
        Args:
            satellite_idx: 卫星索引
            visibility_matrices: 可见性矩阵字典
            distances: 距离矩阵字典
            
        Returns:
            action_mapping: 动作索引到目标的映射
            action_mask: 有效动作掩码
        """
        action_mapping = {}
        action_mask = np.zeros(9, dtype=bool)
        
        # 0: 本地处理（始终可用）
        action_mapping[0] = ('local', satellite_idx)
        action_mask[0] = True
        
        # 1-6: 邻居卫星（固定选择6个，不足时用空占位）
        satellite_visibility = visibility_matrices['sat_to_sat'][satellite_idx]
        visible_satellites = np.where(satellite_visibility)[0]
        visible_satellites = visible_satellites[visible_satellites != satellite_idx]
        
        if len(visible_satellites) > 0:
            # 选择6个邻居卫星（可基于距离、ID或其他策略）
            # 如果可见卫星不足6个，剩余位置保持为无效动作
            if len(visible_satellites) >= 6:
                # 可选：按距离、按ID、或其他策略选择
                sat_distances = distances['sat_to_sat'][satellite_idx, visible_satellites]
                sorted_indices = np.argsort(sat_distances)
                selected_neighbors = visible_satellites[sorted_indices[:6]]
            else:
                selected_neighbors = visible_satellites
            
            # 填充邻居卫星位置（1-6）
            for i, neighbor_idx in enumerate(selected_neighbors):
                action_mapping[i+1] = ('satellite', neighbor_idx)
                action_mask[i+1] = True
            
            # 如果邻居不足6个，剩余位置保持为无效
            # action_mask中对应位置保持False
        
        # 7: 云中心（从可见云中心中选择最近的一个）
        cloud_visibility = visibility_matrices['sat_to_cloud'][satellite_idx]
        visible_clouds = np.where(cloud_visibility)[0]
        
        if len(visible_clouds) > 0:
            # 从可见云中心中选择距离最近的一个
            cloud_distances = distances['sat_to_cloud'][satellite_idx, visible_clouds]
            nearest_cloud_idx = visible_clouds[np.argmin(cloud_distances)]
            action_mapping[7] = ('cloud', nearest_cloud_idx)
            action_mask[7] = True
        
        # 8: 丢弃任务（始终可用）
        action_mapping[8] = ('drop', None)
        action_mask[8] = True
        
        return action_mapping, action_mask
```

#### 5.3 环境提供的动作空间定义

```python
class EnvironmentActionSpace:
    """
    环境层的动作空间定义
    只定义结构，不包含策略生成逻辑
    """
    
    def __init__(self, max_tasks=100):
        # 环境接收的动作格式
        self.action_space = gymnasium.spaces.Dict({
            # 卸载策略向量 - 为每个任务指定处理位置（0-8）
            "offloading_decisions": spaces.MultiDiscrete([9] * max_tasks),
            
            # 本地资源分配向量 - 为本地处理的任务分配CPU资源
            "resource_allocation": spaces.Box(low=0, high=1, shape=(max_tasks,), dtype=np.float32)
        })
        
    def validate_action(self, action, action_mask):
        """验证动作的有效性"""
        # 检查动作是否符合当前的动态约束
        pass
```

### 6. 观测空间设计

基于Dec-POMDP的部分可观测设计，**删除所有内存相关内容，邻居数量限制为6个，包含云中心信息**：

```python
observation_space = gymnasium.spaces.Dict({
    # 自身状态 (6维) - 删除内存相关和任务统计
    "self_state": spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32),
    # 包含: 位置(3), 能量(1), CPU利用率(1), 时间信息(1)
    
    # 邻居卫星状态 (最多6个邻居，每个6维)
    "neighbor_states": spaces.Box(low=0, high=1, shape=(6, 6), dtype=np.float32),
    # 每个邻居: 相对位置(3), 链路质量(1), CPU利用率(1), 队列长度(1)
    
    # 可见云中心状态 (1个云中心，5维)
    "cloud_state": spaces.Box(low=0, high=1, shape=(5,), dtype=np.float32),
    # 云中心: 相对位置(3), 链路质量(1), CPU利用率(1)
    
    # 任务序列 (最多100个任务，每个9维) - 删除内存需求
    "task_sequence": spaces.Box(low=0, high=1, shape=(100, 9), dtype=np.float32),
    # 每个任务: 数据大小(1), 计算复杂度(1), 优先级(1),
    # 已等待时间(1), 剩余截止时间(1), 来源用户ID(1), 
    # 处理进度(1), 任务类型(2)
    
    # 通信链路矩阵 (6x6，与可见卫星的链路质量)
    "communication_links": spaces.Box(low=0, high=1, shape=(6, 6), dtype=np.float32),
    
    # 区域负载信息
    "regional_load": spaces.Box(low=0, high=1, shape=(4,), dtype=np.float32),
    # 包含: 平均CPU利用率, 平均队列长度, 负载方差, 区域任务总数
    
    # 动作空间信息
    "action_mapping": spaces.Box(low=-1, high=100, shape=(9, 2), dtype=np.int32),
    # 每个动作选项的目标类型和索引
    
    # 掩码信息
    "neighbor_mask": spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32),
    "task_mask": spaces.Box(low=0, high=1, shape=(100,), dtype=np.float32),
    "action_mask": spaces.Box(low=0, high=1, shape=(9,), dtype=np.float32)
})
```

### 7. 奖励函数设计（基于Dec-POMDP）

根据Dec-POMDP建模，采用分层奖励机制：

#### 个体奖励 (Individual Reward)
```python
# 基于优先级的任务完成奖励
r_j_local = sum_p(alpha_p * R_j_comp_p) - beta * E_j_cons - zeta * D_j_delay

# 权重参数（来自Dec-POMDP文档）
alpha_1 = 10.0  # 高优先级任务完成奖励
alpha_2 = 6.0   # 中优先级任务完成奖励  
alpha_3 = 3.0   # 低优先级任务完成奖励
beta = 0.1      # 能耗惩罚系数
zeta = 0.01     # 延迟惩罚系数
```

#### 区域协作奖励 (Regional Reward)
```python
# 负载均衡奖励
r_j_regional = -delta * B_k(t)  # B_k(t)为区域k的负载方差
delta = 0.5  # 负载不均衡惩罚系数
```

#### 总奖励
```python
r_j(s, a) = r_j_local(s, a) + r_j_regional(s, a)
```

### 8. 终止与截断条件

- **终止 (Termination)**: 环境不会提前终止
- **截断 (Truncation)**: 当仿真步数达到2000步时截断（基于config.yaml中的`total_timeslots`）

## 第二部分：代码结构设计

### 1. 项目目录结构

```
src/
├── env/
│   ├── Foundation_Layer/          # [已存在] 基础层
│   ├── physics_layer/             # [已存在] 物理层  
│   ├── satellite_cloud/           # [已存在] 计算层
│   ├── env_data/                  # [已存在] 数据文件
│   ├── metrics_model/             # [已存在] 指标模型
│   │
│   └── pettingzoo_env/           # [新建] PettingZoo环境文件夹
│       ├── __init__.py
│       ├── leo_satellite_env.py   # 主环境类
│       ├── pettingzoo_config.yaml # PettingZoo专用配置
│       ├── reward_calculator.py   # 奖励计算
│       ├── observation_builder.py # 观测构建
│       ├── action_processor.py    # 动作处理（处理不可分割任务）
│       └── dynamic_action_space.py # 动态动作空间
│
├── algorithms/
│   └── mappo/                    # [新建] MAPPO算法实现
│       ├── mappo_trainer.py      # MAPPO训练器
│       ├── transformer_policy.py # Transformer策略网络（生成式动作）
│       ├── critic_network.py     # Critic网络
│       └── buffer.py             # 经验回放
│
└── test/
    ├── env/
    │   └── pettingzoo_env/       # [新建] 环境测试
    │       ├── test_env.py
    │       ├── test_reward.py
    │       └── test_action_space.py
    │
    └── algorithms/
        └── mappo/                # [新建] 算法测试
            └── test_transformer_policy.py
```

### 2. 核心模块实现

#### 2.1 主环境类 (`leo_satellite_env.py`)

```python
import pettingzoo
import torch
import numpy as np
from typing import Dict, List, Optional
from src.env.Foundation_Layer.time_manager import TimeManager
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.satellite_cloud.compute_manager import ComputeManager
from .dynamic_action_space import DynamicActionSpace
from .observation_builder import ObservationBuilder
from .reward_calculator import RewardCalculator
from .action_processor import ActionProcessor

class LEOSatelliteParallelEnv(pettingzoo.ParallelEnv):
    """
    LEO卫星边缘计算多智能体环境
    集成现有SPACE2组件，实现PettingZoo Parallel API
    提供动态动作空间信息，支持不可分割任务处理
    """
    
    metadata = {
        "render_modes": ["human", "rgb_array"],
        "name": "leo_satellite_edge_computing_v0",
    }
    
    def __init__(self, config_path="src/env/physics_layer/config.yaml",
                 pettingzoo_config_path="src/env/pettingzoo_env/pettingzoo_config.yaml"):
        """初始化环境，复用现有SPACE2组件"""
        super().__init__()
        
        # 加载配置
        self.config = self._load_config(config_path)
        self.pz_config = self._load_config(pettingzoo_config_path)
        
        # 初始化现有SPACE2组件
        self.time_manager = TimeManager(self.config)
        self.orbital_updater = OrbitalUpdater(self.config)
        self.comm_manager = CommunicationManager(self.config)
        self.compute_manager = ComputeManager(self.config)
        
        # 初始化PettingZoo特定组件
        self.dynamic_action_space = DynamicActionSpace(self.config)
        self.observation_builder = ObservationBuilder(self.config, self.pz_config)
        self.reward_calculator = RewardCalculator(self.config, self.pz_config)
        self.action_processor = ActionProcessor(self.config)
        
        # 初始化智能体
        self.num_satellites = self.config['system']['num_leo_satellites']  # 72
        self.agents = [f"satellite_{i}" for i in range(self.num_satellites)]
        self.possible_agents = self.agents[:]
        
        # 初始化动作和观测空间
        self._init_spaces()
        
        # GPU加速支持
        self.device = self._init_device()
        
        # 环境状态
        self.current_timeslot = 0
        self.action_mappings = {}  # 存储每个智能体的动态动作映射
        
    def _init_spaces(self):
        """初始化标准PettingZoo空间"""
        from gymnasium import spaces
        
        # 观测空间
        obs_space = spaces.Dict({
            "self_state": spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32),
            "neighbor_states": spaces.Box(low=0, high=1, shape=(6, 6), dtype=np.float32),
            "cloud_state": spaces.Box(low=0, high=1, shape=(5,), dtype=np.float32),
            "task_sequence": spaces.Box(low=0, high=1, shape=(100, 9), dtype=np.float32),
            "communication_links": spaces.Box(low=0, high=1, shape=(6, 6), dtype=np.float32),
            "regional_load": spaces.Box(low=0, high=1, shape=(4,), dtype=np.float32),
            "action_mapping": spaces.Box(low=-1, high=100, shape=(9, 2), dtype=np.int32),
            "neighbor_mask": spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32),
            "task_mask": spaces.Box(low=0, high=1, shape=(100,), dtype=np.float32),
            "action_mask": spaces.Box(low=0, high=1, shape=(9,), dtype=np.float32),
        })
        
        # 动作空间（只包含环境需要的动作，不包含掩码）
        act_space = spaces.Dict({
            "offloading_decisions": spaces.MultiDiscrete([9] * 100),  # 离散动作
            "resource_allocation": spaces.Box(low=0, high=1, shape=(100,), dtype=np.float32),
        })
        
        # 为每个智能体分配空间
        self.observation_spaces = {agent: obs_space for agent in self.agents}
        self.action_spaces = {agent: act_space for agent in self.agents}
        
    def _init_device(self):
        """初始化计算设备（GPU/CPU）"""
        if self.config['gpu']['enable_gpu_acceleration'] and torch.cuda.is_available():
            return torch.device('cuda')
        return torch.device('cpu')
    
    def reset(self, seed=None, options=None):
        """重置环境"""
        super().reset(seed=seed)
        
        self.current_timeslot = 0
        self.action_mappings = {}
        
        # 重置各组件
        self.time_manager.reset()
        self.compute_manager.reset()
        
        # 生成初始观测
        observations = self._generate_observations()
        infos = {agent: {} for agent in self.agents}
        
        return observations, infos
    
    def step(self, actions):
        """环境步进，处理动态动作空间"""
        # 初始化返回值
        observations = {}
        rewards = {}
        terminations = {agent: False for agent in self.agents}
        truncations = {agent: False for agent in self.agents}
        infos = {agent: {} for agent in self.agents}
        
        # 阶段0: 时间推进
        self.current_timeslot += 1
        time_context = self.time_manager.get_time_context(self.current_timeslot)
        
        # 阶段1: 物理更新（使用真实数据）
        satellites = self.orbital_updater.get_satellites_at_time(self.current_timeslot)
        visibility_matrices = self.orbital_updater.get_visibility_matrices(self.current_timeslot)
        distances = self._compute_distances(satellites, visibility_matrices)
        
        # 阶段2: 更新动态动作空间
        self._update_action_mappings(visibility_matrices, distances)
        
        # 阶段3: 处理动作
        self._process_actions(actions)
        
        # 阶段4: 执行计算
        computation_results = self.compute_manager.process_tasks(self.current_timeslot)
        
        # 阶段5: 计算奖励
        for agent_id in self.agents:
            rewards[agent_id] = self.reward_calculator.calculate(
                agent_id, computation_results, self.action_mappings[agent_id]
            )
        
        # 阶段6: 生成观测
        observations = self._generate_observations()
        
        # 阶段7: 检查终止条件
        if self.current_timeslot >= self.config['system']['total_timeslots']:
            truncations = {agent: True for agent in self.agents}
        
        return observations, rewards, terminations, truncations, infos
    
    def _update_action_mappings(self, visibility_matrices, distances):
        """更新每个智能体的动态动作映射"""
        for agent_id in self.agents:
            satellite_idx = int(agent_id.split('_')[1])
            mapping, mask = self.dynamic_action_space.compute_action_space(
                satellite_idx, visibility_matrices, distances
            )
            self.action_mappings[agent_id] = {
                'mapping': mapping,
                'mask': mask
            }
    
    def _process_actions(self, actions):
        """处理基于动态动作空间的动作"""
        for agent_id, action in actions.items():
            satellite_idx = int(agent_id.split('_')[1])
            action_mapping = self.action_mappings[agent_id]['mapping']
            
            self.action_processor.process(
                satellite_idx, action, action_mapping, 
                self.compute_manager
            )
    
    def _compute_distances(self, satellites, visibility_matrices):
        """
        计算距离矩阵
        使用真实的卫星位置和云中心位置数据
        """
        # 直接使用orbital_updater计算的真实距离
        # orbital模块已经计算了所有距离
        distances = self.orbital_updater.compute_distances(self.current_timeslot)
        
        # 返回真实的距离数据
        return {
            'sat_to_sat': distances['satellite_distances'],     # 72x72 真实卫星间距离
            'sat_to_cloud': distances['satellite_cloud_distances']  # 72x5 真实卫星-云距离
        }
    
    def _generate_observations(self):
        """生成所有智能体的观测"""
        observations = {}
        
        for agent_id in self.agents:
            satellite_idx = int(agent_id.split('_')[1])
            
            # 使用observation_builder生成观测
            observations[agent_id] = self.observation_builder.build(
                agent_id,
                self.current_satellites,
                self.current_visibility_matrices,
                self.current_link_qualities,
                self.action_mappings.get(agent_id, {}).get('mapping', {}),
                self.action_mappings.get(agent_id, {}).get('mask', np.zeros(9))
            )
        
        return observations
```

#### 2.2 动作处理器（处理不可分割任务） (`action_processor.py`)

```python
class ActionProcessor:
    """
    处理基于动态动作空间的动作执行
    确保任务的原子性（不可分割）
    """
    
    def __init__(self, config):
        self.config = config
        
    def process(self, satellite_idx, action, action_mapping, compute_manager):
        """
        处理单个卫星的动作序列
        每个任务必须完整地在一个位置处理
        
        Args:
            satellite_idx: 卫星索引
            action: 动作字典（包含offloading_decisions和resource_allocation）
            action_mapping: 动作索引到目标的映射
            compute_manager: 计算管理器
        """
        offloading_decisions = action['offloading_decisions']
        resource_allocation = action['resource_allocation']
        
        # 获取任务队列
        task_queue = compute_manager.get_task_queue(satellite_idx)
        real_queue_len = len(task_queue)
        
        # 环境自己管理任务有效性，不依赖策略的mask
        for task_idx in range(min(real_queue_len, len(offloading_decisions))):
            task = task_queue[task_idx]
            decision = int(offloading_decisions[task_idx])  # 确保是整数
            
            # 检查动作有效性（基于环境的action_mapping）
            if decision not in action_mapping:
                continue  # 忽略无效动作
                
            target_type, target_idx = action_mapping[decision]
            
            # 确保任务的原子性处理
            if target_type == 'local':
                # 本地完整处理任务
                cpu_allocation = resource_allocation[task_idx]
                compute_manager.process_task_locally(
                    satellite_idx, task, cpu_allocation
                )
                
            elif target_type == 'satellite':
                # 完整迁移任务到邻居卫星
                # 任务将在目标卫星上从头开始处理
                compute_manager.migrate_complete_task(
                    task, satellite_idx, target_idx
                )
                
            elif target_type == 'cloud':
                # 完整卸载任务到云中心
                # 任务将在云中心完整处理
                compute_manager.offload_complete_task(
                    task, satellite_idx, target_idx
                )
                
            elif target_type == 'drop':
                # 丢弃整个任务
                compute_manager.drop_task(satellite_idx, task)
    
    def validate_task_atomicity(self, task):
        """
        验证任务的原子性约束
        确保任务不会被分割
        """
        assert task.status in ['queued', 'processing', 'completed', 'dropped']
        assert not hasattr(task, 'partial_progress')  # 不存在部分进度
        return True
```

## 第三部分：MAPPO算法层实现

### 1. Transformer策略网络 (`algorithms/mappo/transformer_policy.py`)

```python
import torch
import torch.nn as nn
import torch.nn.functional as F

class TransformerPolicy(nn.Module):
    """
    Transformer作为MAPPO的策略网络π(a|s)
    生成式动作空间，支持动态动作掩码
    位于MAPPO算法层，不在环境内部
    """
    
    def __init__(self, config):
        super().__init__()
        self.config = config['pettingzoo']['transformer']
        
        # 输入嵌入层
        self.state_embedding = nn.Linear(6, self.config['d_model'])
        self.task_embedding = nn.Linear(9, self.config['d_model'])
        self.neighbor_embedding = nn.Linear(6, self.config['d_model'])
        self.cloud_embedding = nn.Linear(5, self.config['d_model'])
        self.action_info_embedding = nn.Linear(18, self.config['d_model'])  # 9*2的动作映射信息
        
        # 位置编码
        self.positional_encoding = nn.Parameter(
            torch.randn(1, self.config['max_sequence_length'] + 10, self.config['d_model'])
        )
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.config['d_model'],
            nhead=self.config['nhead'],
            dim_feedforward=self.config['dim_feedforward'],
            dropout=self.config['dropout'],
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=self.config['num_encoder_layers']
        )
        
        # 输出头（支持9个动作选项）
        self.offloading_head = nn.Linear(self.config['d_model'], 9)
        self.resource_head = nn.Linear(self.config['d_model'], 1)
        
    def forward(self, observation):
        """
        前向传播，生成考虑动态动作空间的动作序列
        """
        # 提取观测组件
        self_state = observation['self_state']
        task_sequence = observation['task_sequence']
        neighbor_states = observation['neighbor_states']
        cloud_state = observation['cloud_state']
        action_mapping = observation['action_mapping']
        action_mask = observation['action_mask']
        task_mask = observation['task_mask']
        neighbor_mask = observation['neighbor_mask']
        
        batch_size = self_state.shape[0]
        
        # 嵌入各组件
        state_emb = self.state_embedding(self_state).unsqueeze(1)
        task_emb = self.task_embedding(task_sequence)
        
        # 处理邻居嵌入（考虑掩码）
        neighbor_emb = self.neighbor_embedding(neighbor_states)
        neighbor_emb = neighbor_emb * neighbor_mask.unsqueeze(-1)
        
        # 云中心嵌入
        cloud_emb = self.cloud_embedding(cloud_state).unsqueeze(1)
        
        # 动作空间信息嵌入（转换为float）
        action_info = action_mapping.float().flatten(-2)  # 确保是float类型
        action_info_emb = self.action_info_embedding(action_info).unsqueeze(1)
        
        # 构建输入序列
        # [CLS, state, action_info, cloud, neighbors, tasks]
        sequence = torch.cat([
            state_emb,
            action_info_emb,
            cloud_emb,
            neighbor_emb,
            task_emb
        ], dim=1)
        
        # 添加位置编码
        seq_len = sequence.shape[1]
        sequence = sequence + self.positional_encoding[:, :seq_len, :]
        
        # 构建注意力掩码
        attention_mask = self._build_attention_mask(
            batch_size, neighbor_mask, task_mask
        )
        
        # Transformer编码
        encoded = self.transformer(sequence, src_key_padding_mask=attention_mask)
        
        # 提取任务对应的输出
        num_fixed_tokens = 4 + neighbor_states.shape[1]  # CLS + action_info + cloud + neighbors
        task_outputs = encoded[:, num_fixed_tokens:, :]
        
        # 生成动作（考虑动作掩码）
        offloading_logits = self.offloading_head(task_outputs)
        
        # 应用动作掩码
        action_mask_expanded = action_mask.unsqueeze(1).expand(-1, task_outputs.shape[1], -1)
        masked_logits = offloading_logits.masked_fill(~action_mask_expanded.bool(), -1e9)
        
        # 采样离散动作（而不是返回logits）
        probs = torch.softmax(masked_logits, dim=-1)
        dist = torch.distributions.Categorical(probs=probs)
        actions = dist.sample()  # (batch_size, num_tasks) 整数值 ∈ [0, 8]
        
        # 资源分配（加入约束）
        resource_allocation = torch.sigmoid(self.resource_head(task_outputs)).squeeze(-1)
        
        # 对本地处理的任务进行资源归一化
        resource_allocation = self._normalize_resource_allocation(
            actions, resource_allocation
        )
        
        return {
            'offloading_decisions': actions,  # 返回离散动作索引
            'resource_allocation': resource_allocation,
            'log_probs': dist.log_prob(actions)  # 用于PPO训练
        }
    
    def _normalize_resource_allocation(self, actions, allocations):
        """
        确保本地处理任务的总资源分配不超过1
        """
        batch_size = actions.shape[0]
        normalized = allocations.clone()
        
        for b in range(batch_size):
            local_mask = (actions[b] == 0)  # 0表示本地处理
            if local_mask.any():
                local_allocs = allocations[b][local_mask]
                total = local_allocs.sum()
                if total > 1:
                    normalized[b][local_mask] = local_allocs / total
        
        return normalized
    
    def _build_attention_mask(self, batch_size, neighbor_mask, task_mask):
        """构建注意力掩码"""
        # 实现注意力掩码逻辑
        pass
```

### 2. MAPPO训练器 (`algorithms/mappo/mappo_trainer.py`)

```python
class MAPPOTrainer:
    """
    MAPPO训练器，集成Transformer策略网络
    处理PettingZoo环境的动态动作空间
    """
    
    def __init__(self, env, config):
        self.env = env
        self.config = config
        
        # 初始化网络
        self.actor = TransformerPolicy(config)  # Transformer策略网络
        self.critic = CriticNetwork(config)
        
        # 优化器
        self.actor_optimizer = torch.optim.Adam(
            self.actor.parameters(), 
            lr=config['learning_rate']
        )
        self.critic_optimizer = torch.optim.Adam(
            self.critic.parameters(), 
            lr=config['learning_rate']
        )
        
    def collect_rollouts(self):
        """收集训练数据"""
        observations = self.env.reset()
        
        for step in range(self.config['rollout_steps']):
            # 从环境获取动作掩码
            action_masks = {
                agent: obs['action_mask'] 
                for agent, obs in observations.items()
            }
            
            # 使用Transformer生成动作
            actions = {}
            for agent, obs in observations.items():
                action = self.actor(obs, action_masks[agent])
                actions[agent] = action
            
            # 环境步进
            next_obs, rewards, dones, truncs, infos = self.env.step(actions)
            
            # 存储经验
            self.buffer.store(observations, actions, rewards, next_obs)
            
            observations = next_obs
    
    def train_step(self):
        """训练步骤"""
        # 从buffer采样
        batch = self.buffer.sample()
        
        # 计算优势函数
        advantages = self.compute_advantages(batch)
        
        # 更新Actor（策略网络）
        policy_loss = self.compute_policy_loss(batch, advantages)
        self.actor_optimizer.zero_grad()
        policy_loss.backward()
        self.actor_optimizer.step()
        
        # 更新Critic（价值网络）
        value_loss = self.compute_value_loss(batch)
        self.critic_optimizer.zero_grad()
        value_loss.backward()
        self.critic_optimizer.step()
```

### 3. PettingZoo专用配置 (`pettingzoo_config.yaml`)

```yaml
# PettingZoo环境专用配置
pettingzoo:
  # 观测空间参数
  observation:
    num_neighbors: 6           # 固定邻居数量
    max_tasks: 100            # 最大任务数量
    self_state_dim: 6         # 自身状态维度（不含内存和任务统计）
    neighbor_state_dim: 6     # 邻居状态维度（不含内存）
    cloud_state_dim: 5        # 云中心状态维度
    task_feature_dim: 9       # 任务特征维度（不含内存）
    
  # 动作空间参数  
  action:
    num_action_targets: 9     # 动作目标数：本地(1)+邻居(6)+云(1)+丢弃(1)
    enable_resource_allocation: true
    enable_dynamic_masking: true
    
  # Transformer策略网络参数
  transformer:
    d_model: 256
    nhead: 8
    num_encoder_layers: 6
    dim_feedforward: 1024
    dropout: 0.1
    max_sequence_length: 100
    use_flash_attention: true
    
  # 奖励权重（基于Dec-POMDP）
  reward:
    # 个体奖励
    alpha_1: 10.0    # 高优先级任务完成
    alpha_2: 6.0     # 中优先级任务完成
    alpha_3: 3.0     # 低优先级任务完成
    beta: 0.1        # 能耗惩罚
    zeta: 0.01       # 延迟惩罚
    timeout_penalty: -5.0  # 任务超时惩罚
    
    # 区域协作奖励
    delta: 0.5       # 负载不均衡惩罚
    
  # 动态动作空间配置
  dynamic_action:
    satellite_distance_threshold_km: 2000  # 卫星间可见距离阈值
    cloud_distance_threshold_km: 3300      # 卫星-云可见距离阈值
    num_neighbor_satellites: 6             # 固定选择的邻居卫星数
    select_nearest_cloud: true             # 选择最近的云中心
```

## 第四部分：GPU/Torch优化方案

### 1. 批量处理优化

```python
class GPUOptimizedProcessor:
    """
    GPU优化的批量处理器
    """
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.batch_threshold = config['gpu']['batch_size_threshold']
        
    def batch_compute_dynamic_action_spaces(self, visibility_matrices, distances):
        """
        批量计算所有卫星的动态动作空间（简化但可运行的版本）
        """
        num_satellites = self.config['system']['num_leo_satellites']
        
        # 转换为tensor
        sat_visibility = torch.tensor(
            visibility_matrices['sat_to_sat'], 
            device=self.device, dtype=torch.float32
        )
        cloud_visibility = torch.tensor(
            visibility_matrices['sat_to_cloud'], 
            device=self.device, dtype=torch.float32
        )
        sat_distances = torch.tensor(
            distances['sat_to_sat'], 
            device=self.device, dtype=torch.float32
        )
        cloud_distances = torch.tensor(
            distances['sat_to_cloud'], 
            device=self.device, dtype=torch.float32
        )
        
        # 批量处理：使用向量化操作
        # 1. 设置对角线为无穷大（排除自己）
        sat_distances_masked = sat_distances + torch.eye(num_satellites, device=self.device) * 1e9
        
        # 2. 只考虑可见的卫星
        sat_distances_masked = torch.where(
            sat_visibility > 0,
            sat_distances_masked,
            torch.tensor(1e9, device=self.device)
        )
        
        # 3. 选择6个邻居（可基于距离、ID或其他策略）
        # 这里使用距离策略，但固定选择6个
        k = min(6, (sat_visibility.sum(dim=1) - 1).min().item())  # 确保不超过最少可见数
        if k > 0:
            _, nearest_neighbors = torch.topk(
                -sat_distances_masked,  # 负值以获得最小距离
                k=k,
                dim=1
            )
        else:
            nearest_neighbors = torch.zeros((num_satellites, 0), dtype=torch.long, device=self.device)
        
        # 4. 处理云中心（从可见云中选择最近的一个）
        cloud_distances_masked = torch.where(
            cloud_visibility > 0,
            cloud_distances,
            torch.tensor(1e9, device=self.device)
        )
        nearest_clouds = torch.argmin(cloud_distances_masked, dim=1)
        has_cloud = (cloud_visibility.sum(dim=1) > 0)
        
        # 5. 构建动作掩码（基于实际可见性）
        action_masks = torch.zeros((num_satellites, 9), device=self.device, dtype=torch.bool)
        action_masks[:, 0] = True  # 本地始终可用
        action_masks[:, 8] = True  # 丢弃始终可用
        
        # 邻居掩码（基于实际选择的邻居）
        if k > 0:
            for i in range(num_satellites):
                visible_count = (sat_visibility[i] > 0).sum() - 1  # 减去自己
                actual_neighbors = min(visible_count.item(), k)
                action_masks[i, 1:1+actual_neighbors] = True
        
        # 云中心掩码（只有可见云时才为True）
        action_masks[:, 7] = has_cloud
        
        # 返回简化的结果
        return nearest_neighbors.cpu().numpy(), action_masks.cpu().numpy()
    
    def _build_action_mapping(self, sat_idx, selected_neighbors, nearest_cloud):
        """构建动作映射"""
        mapping = {}
        mask = torch.zeros(9, dtype=torch.bool, device=self.device)
        
        # 本地处理
        mapping[0] = ('local', sat_idx)
        mask[0] = True
        
        # 邻居卫星
        for i, neighbor in enumerate(selected_neighbors):
            mapping[i+1] = ('satellite', neighbor.item())
            mask[i+1] = True
        
        # 云中心
        if nearest_cloud is not None:
            mapping[7] = ('cloud', nearest_cloud.item())
            mask[7] = True
        
        # 丢弃
        mapping[8] = ('drop', None)
        mask[8] = True
        
        return mapping, mask
```

### 2. 并行环境执行优化

```python
class ParallelEnvironmentExecutor:
    """
    并行环境执行器，利用GPU加速
    """
    
    def __init__(self, env_class, num_envs=4):
        self.num_envs = num_envs
        self.envs = [env_class() for _ in range(num_envs)]
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    @torch.no_grad()
    def parallel_step(self, actions_batch):
        """
        并行执行多个环境的步进
        """
        # 收集所有环境的状态
        states = self._collect_states()
        
        # GPU上批量计算物理更新
        physics_batch = self._batch_physics_update(states)
        
        # 批量计算动态动作空间
        action_spaces_batch = self._batch_compute_action_spaces(physics_batch)
        
        # 批量处理动作
        results_batch = self._batch_process_actions(
            actions_batch, action_spaces_batch
        )
        
        # 批量计算奖励
        rewards_batch = self._batch_compute_rewards(results_batch)
        
        # 批量生成观测
        observations_batch = self._batch_generate_observations(
            physics_batch, action_spaces_batch
        )
        
        return observations_batch, rewards_batch
    
    def _batch_physics_update(self, states):
        """批量物理更新"""
        # 实现批量轨道更新和通信计算
        pass
    
    def _batch_compute_action_spaces(self, physics_batch):
        """批量计算动态动作空间"""
        # 使用GPU加速的动作空间计算
        pass
```

## 第五部分：关键实现细节

### 1. 观测生成器 (`observation_builder.py`)

```python
class ObservationBuilder:
    """
    构建包含动态动作空间信息的观测
    重要：neighbor_states的顺序必须与action_mapping中的邻居顺序严格一致
    """
    
    def __init__(self, config, pz_config):
        self.config = config
        self.pz_config = pz_config
        self.num_neighbors = 6  # 固定6个邻居
        self.max_tasks = pz_config['observation']['max_tasks']
        
    def build(self, agent_id, satellites, visibility_matrices, 
              link_qualities, action_mapping, action_mask):
        """
        构建单个智能体的观测
        """
        satellite_idx = int(agent_id.split('_')[1])
        
        # 1. 自身状态
        self_state = self._build_self_state(satellite_idx, satellites)
        
        # 2. 邻居状态（固定6个）
        neighbor_states, neighbor_mask = self._build_neighbor_states(
            satellite_idx, satellites, visibility_matrices, link_qualities
        )
        
        # 3. 云中心状态（最近的1个）
        cloud_state = self._build_cloud_state(
            satellite_idx, visibility_matrices, link_qualities
        )
        
        # 4. 任务序列
        task_sequence, task_mask = self._build_task_sequence(satellite_idx)
        
        # 5. 通信链路矩阵
        comm_links = self._build_communication_matrix(
            satellite_idx, visibility_matrices, link_qualities
        )
        
        # 6. 区域负载信息
        regional_load = self._build_regional_load(satellite_idx)
        
        # 7. 动作空间信息
        action_mapping_array = self._encode_action_mapping(action_mapping)
        
        return {
            'self_state': self_state,
            'neighbor_states': neighbor_states,
            'cloud_state': cloud_state,
            'task_sequence': task_sequence,
            'communication_links': comm_links,
            'regional_load': regional_load,
            'action_mapping': action_mapping_array,
            'neighbor_mask': neighbor_mask,
            'task_mask': task_mask,
            'action_mask': action_mask
        }
    
    def _build_neighbor_states(self, satellite_idx, action_mapping, satellites, link_qualities):
        """
        构建邻居状态矩阵
        关键：按照action_mapping中的顺序排列邻居
        action_mapping[1-6]对应的邻居卫星将按顺序填入neighbor_states
        """
        neighbor_states = np.zeros((6, 6))  # 固定6个邻居
        neighbor_mask = np.zeros(6)
        
        # 严格按照action_mapping的顺序填充
        # 固定6个位置，可能有空位
        for i in range(1, 7):  # action索引1-6对应邻居
            if i in action_mapping:
                target_type, neighbor_idx = action_mapping[i]
                if target_type == 'satellite':
                    neighbor_states[i-1] = self._get_neighbor_features(
                        satellite_idx, neighbor_idx, satellites, link_qualities
                    )
                    neighbor_mask[i-1] = 1.0
                # 如果某个位置没有邻岅，保持为0
        
        return neighbor_states, neighbor_mask
    
    def _encode_action_mapping(self, action_mapping):
        """
        编码动作映射为数组形式
        约定：type编码 - 0=local, 1=satellite, 2=cloud, 3=drop
        """
        encoded = np.full((9, 2), -1, dtype=np.int32)
        
        for action_idx, (target_type, target_idx) in action_mapping.items():
            if target_type == 'local':
                encoded[action_idx] = [0, target_idx if target_idx else -1]
            elif target_type == 'satellite':
                encoded[action_idx] = [1, target_idx]
            elif target_type == 'cloud':
                encoded[action_idx] = [2, target_idx]
            elif target_type == 'drop':
                encoded[action_idx] = [3, -1]
        
        return encoded
```

### 2. 奖励计算器 (`reward_calculator.py`)

```python
class RewardCalculator:
    """
    基于Dec-POMDP的奖励计算器
    """
    
    def __init__(self, config, pz_config):
        self.config = config
        self.reward_config = pz_config['reward']
        
    def calculate(self, agent_id, computation_results):
        """
        计算单个智能体的奖励
        """
        satellite_idx = int(agent_id.split('_')[1])
        
        # 个体奖励
        individual_reward = self._calculate_individual_reward(
            satellite_idx, computation_results
        )
        
        # 区域协作奖励
        regional_reward = self._calculate_regional_reward(
            satellite_idx, computation_results
        )
        
        total_reward = individual_reward + regional_reward
        
        return total_reward
    
    def _calculate_individual_reward(self, satellite_idx, results):
        """计算个体奖励"""
        reward = 0.0
        
        # 任务完成奖励（按优先级）
        for priority in [1, 2, 3]:
            completed = results[f'completed_p{priority}'][satellite_idx]
            weight = self.reward_config[f'alpha_{priority}']
            reward += completed * weight
        
        # 能耗惩罚
        energy_consumed = results['energy_consumption'][satellite_idx]
        reward -= self.reward_config['beta'] * energy_consumed
        
        # 延迟惩罚
        avg_delay = results['average_delay'][satellite_idx]
        reward -= self.reward_config['zeta'] * avg_delay
        
        # 超时惩罚
        timeout_count = results['timeout_tasks'][satellite_idx]
        reward += self.reward_config['timeout_penalty'] * timeout_count
        
        return reward
    
    def _calculate_regional_reward(self, satellite_idx, results):
        """计算区域协作奖励"""
        region_id = self._get_region_id(satellite_idx)
        load_variance = results['regional_load_variance'][region_id]
        
        return -self.reward_config['delta'] * load_variance
```

### 3. 测试策略

```python
class TestDynamicActionSpace:
    """
    测试动态动作空间的正确性
    """
    
    def test_action_space_generation(self):
        """测试动作空间生成"""
        env = LEOSatelliteParallelEnv()
        env.reset()
        
        # 测试不同时隙的动作空间
        for timeslot in [1, 100, 500, 1000]:
            print(f"\n=== 时隙 {timeslot} 动作空间测试 ===")
            
            env.current_timeslot = timeslot
            satellites = env.orbital_updater.get_satellites_at_time(timeslot)
            visibility = env.orbital_updater.get_visibility_matrices(timeslot)
            distances = env._compute_distances(satellites)
            
            # 测试每个卫星的动作空间
            for agent_id in env.agents[:5]:  # 测试前5个卫星
                sat_idx = int(agent_id.split('_')[1])
                mapping, mask = env.dynamic_action_space.compute_action_space(
                    sat_idx, visibility, distances
                )
                
                # 验证动作映射
                assert 0 in mapping  # 本地处理始终可用
                assert 8 in mapping  # 丢弃始终可用
                assert mask[0] and mask[8]  # 对应掩码为True
                
                # 验证邻居卫星数量
                neighbor_count = sum(1 for i in range(1, 7) if mask[i])
                # 邻居数量可能小于6（当可见卫星不足时）
                assert neighbor_count <= 6
                
                # 验证云中心
                has_cloud = mask[7]
                print(f"  卫星{sat_idx}: 邻居={neighbor_count}, 云中心={has_cloud}")
    
    def test_transformer_with_dynamic_action(self):
        """测试Transformer处理动态动作空间"""
        config = load_config()
        policy = TransformerPolicy(config)
        
        # 创建测试观测
        batch_size = 4
        observation = {
            'self_state': torch.randn(batch_size, 6),
            'neighbor_states': torch.randn(batch_size, 6, 6),
            'cloud_state': torch.randn(batch_size, 5),
            'task_sequence': torch.randn(batch_size, 100, 9),
            'action_mapping': torch.randint(-1, 72, (batch_size, 9, 2)),
            'action_mask': torch.bernoulli(torch.ones(batch_size, 9) * 0.7),
            'task_mask': torch.bernoulli(torch.ones(batch_size, 100) * 0.5),
            'neighbor_mask': torch.bernoulli(torch.ones(batch_size, 6) * 0.8)
        }
        
        # 前向传播
        output = policy(observation)
        
        # 验证输出
        assert 'offloading_decisions' in output
        assert 'resource_allocation' in output
        assert output['offloading_decisions'].shape == (batch_size, 100, 9)
        
        # 验证掩码应用
        invalid_actions = ~observation['action_mask'].bool()
        masked_values = output['offloading_decisions'][:, 0, :][invalid_actions[:, :]]
        assert torch.all(masked_values < -1e8)  # 被掩码的动作应该有很小的logit
    
    def test_gpu_acceleration(self):
        """测试GPU加速"""
        if torch.cuda.is_available():
            env = LEOSatelliteParallelEnv()
            assert env.device.type == 'cuda'
            
            # 测试批量处理
            processor = GPUOptimizedProcessor(env.config)
            
            # 模拟数据
            visibility = {
                'sat_to_sat': np.random.rand(72, 72) > 0.5,
                'sat_to_cloud': np.random.rand(72, 5) > 0.7
            }
            distances = {
                'sat_to_sat': np.random.rand(72, 72) * 5000,
                'sat_to_cloud': np.random.rand(72, 5) * 3300
            }
            
            # 批量计算动作空间
            mappings, masks = processor.batch_compute_dynamic_action_spaces(
                visibility, distances
            )
            
            assert len(mappings) == 72
            assert masks.shape == (72, 9)
            print("✓ GPU加速测试通过")
```

## 第六部分：开发实施计划

### 1. 分阶段开发计划

**Phase 1: 基础环境搭建（第1周）**
- 创建`src/env/pettingzoo_env/`文件夹结构
- 实现基础`LEOSatelliteParallelEnv`类
- 集成现有SPACE2组件
- 实现动态动作空间计算
- 确保任务原子性（不可分割）

**Phase 2: MAPPO算法层实现（第2周）**
- 创建`src/algorithms/mappo/`文件夹
- 实现`TransformerPolicy`策略网络
- 实现`MAPPOTrainer`训练器
- 实现动作掩码机制

**Phase 3: 观测和奖励系统（第3周）**
- 实现`ObservationBuilder`（包含动作空间信息）
- 实现`RewardCalculator`（基于Dec-POMDP）
- 实现`ActionProcessor`处理不可分割任务
- 验证任务原子性约束

**Phase 4: GPU优化（第4周）**
- 批量计算动态动作空间
- 优化Transformer推理
- 实现混合精度训练
- 性能基准测试

**Phase 5: 集成测试（第5周）**
- 环境单元测试
- MAPPO算法测试
- PettingZoo API兼容性测试
- 端到端训练测试

### 2. 依赖管理

```txt
# requirements.txt
pettingzoo>=1.24.0
gymnasium>=0.29.0
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
pyyaml>=6.0
torch>=2.0.0  # GPU支持
matplotlib>=3.7.0
```

### 3. 关键配置参数总结

基于`config.yaml`的实际参数：
- **卫星数量**: 72
- **用户数量**: 420  
- **云中心数量**: 5
- **总时隙数**: 2000
- **时隙持续时间**: 3秒
- **LEO卫星CPU频率**: 50 GHz
- **最大并行任务数**: 200
- **最大队列长度**: 100
- **卫星间可见距离**: 5000 km
- **卫星-云可见距离**: 3300 km
- **GPU加速**: 启用

## 总结

本开发文档为SPACE2项目设计了一个完整的基于PettingZoo的多智能体强化学习环境，核心特点：

### 环境层（PettingZoo）
1. **动态动作空间**：根据实时可见性计算，每个卫星固定拥有本地(1)+邻居(6)+云(1)+丢弃(1)=9个动作位置
2. **任务原子性**：所有任务不可分割，必须完整地在一个位置处理
3. **无内存设计**：完全删除内存相关内容
4. **基于Dec-POMDP**：严格的理论建模和奖励设计
5. **复用现有组件**：最大化利用SPACE2已实现的模块

### 算法层（MAPPO）
1. **Transformer策略网络**：作为MAPPO的Actor网络，实现生成式动作空间
2. **动作掩码处理**：根据环境提供的动作有效性信息生成合法动作
3. **GPU优化**：批量处理和混合精度训练

### 架构优势
- **清晰的分层设计**：环境负责状态转移，算法负责策略学习
- **正确的项目结构**：PettingZoo环境在`src/env/pettingzoo_env/`，MAPPO算法在`src/algorithms/mappo/`
- **完全遵循SPACE2编程规范**：绝对导入、配置管理、测试规范

该设计实现了环境与算法的正确解耦，确保了系统的可维护性和可扩展性。

## 数据使用原则

### 严格使用真实数据
1. **距离计算**：直接调用`orbital_updater.compute_distances()`获取真实的卫星间距离和卫星-云距离
2. **可见性矩阵**：使用`orbital_updater.get_visibility_matrices()`获取基于真实轨道的可见性
3. **禁止随机生成**：所有物理数据必须来自现有SPACE2组件的计算结果

### 动作空间选择逻辑
1. **邻居卫星（动作1-6）**：从**可见卫星**中选择6个（可基于距离、ID或其他策略）
2. **云中心（动作7）**：从**可见云中心**中选择距离最近的1个
3. **一致性原则**：可见性是前提条件，距离是排序依据

## 关键问题修复说明

### 已修复的主要问题：

1. **观测空间与动作空间顺序一致性**
   - `neighbor_states`严格按照`action_mapping[1-6]`的顺序排列（固定6个位置）
   - 确保Transformer能正确关联邻居状态与动作目标

2. **移除冗余奖励shaping**
   - 删除了`action_validity_bonus`
   - 奖励仅基于任务完成、能耗、延迟和负载均衡

3. **TransformerPolicy输出格式**
   - 策略网络现在输出离散动作索引（0-8的整数）
   - 不再输出logits，而是通过采样生成具体动作
   - 加入资源分配归一化，确保总和不超过1

4. **ParallelEnv接口合规**
   - 实现了`_init_spaces()`方法
   - 正确定义了`observation_spaces`和`action_spaces`属性
   - 动作空间不包含mask（mask仅在观测中提供）

5. **环境不依赖策略mask**
   - `ActionProcessor`基于实际队列长度管理任务
   - 环境自行检查动作有效性，不依赖策略回传的mask

6. **提供核心方法实现**
   - 实现了`_compute_distances()`基本距离计算
   - 实现了`_generate_observations()`观测生成
   - GPU批处理使用真正的向量化操作（topk选择邻居）

7. **数据类型修复**
   - `action_mapping`转换为float类型后再输入Linear层
   - 确保所有tensor类型匹配

这些修复确保了环境能够正确运行，并与MAPPO算法无缝集成。