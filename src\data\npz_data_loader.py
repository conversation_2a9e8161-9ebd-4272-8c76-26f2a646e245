"""
NPZ数据加载器 - 高效读取时隙分片数据
提供与原JSON格式兼容的接口，同时实现高性能数据访问
"""

import numpy as np
import scipy.sparse as sp
from pathlib import Path
import json
import time
from typing import Dict, List, Any, Optional, Union, Tuple
from functools import lru_cache


class NPZDataLoader:
    def __init__(self, data_dir: str = 'src/data/npz_data'):
        """
        初始化NPZ数据加载器
        
        Args:
            data_dir: NPZ数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.timeslot_dir = self.data_dir / 'timeslots'
        
        # 加载元数据
        self.metadata = self._load_metadata()
        
        # 系统参数
        self.num_timeslots = self.metadata['num_timeslots']
        self.num_satellites = self.metadata['num_satellites']
        self.num_ground_stations = self.metadata['num_ground_stations']
        self.num_cloud_stations = self.metadata['num_cloud_stations']
        
        # 缓存设置
        self._cache_size = 10  # 缓存最近10个时隙
        self._timeslot_cache = {}
        self._access_order = []
        
        # 性能统计
        self.stats = {
            'loads': 0,
            'cache_hits': 0,
            'total_load_time': 0.0
        }
    
    def _load_metadata(self) -> Dict:
        """加载元数据文件"""
        metadata_file = self.data_dir / 'metadata.json'
        if not metadata_file.exists():
            raise FileNotFoundError(f"元数据文件不存在: {metadata_file}")
        
        with open(metadata_file, 'r') as f:
            return json.load(f)
    
    def _load_timeslot_npz(self, timeslot: int) -> Dict[str, Any]:
        """
        从NPZ文件加载单个时隙数据
        
        Args:
            timeslot: 时隙ID
            
        Returns:
            包含该时隙所有数据的字典
        """
        filename = self.timeslot_dir / f'ts_{timeslot:04d}.npz'
        if not filename.exists():
            raise FileNotFoundError(f"时隙文件不存在: {filename}")
        
        start_time = time.time()
        data = np.load(filename, allow_pickle=True)
        load_time = time.time() - start_time
        
        self.stats['loads'] += 1
        self.stats['total_load_time'] += load_time
        
        # 转换为字典格式
        result = {
            'timeslot_id': int(data['timeslot_id']),
            'satellite_states': data['satellite_states'],
            'ground_distance': data['ground_distance'].item(),
            'ground_uplink': data['ground_uplink'].item(),
            'ground_downlink': data['ground_downlink'].item(),
            'cloud_distance': data['cloud_distance'].item(),
            'cloud_s2c': data['cloud_s2c'].item(),
            'cloud_c2s': data['cloud_c2s'].item(),
            'isl_distance': data['isl_distance'].item(),
            'tasks': data['tasks']
        }
        
        data.close()
        return result
    
    def get_timeslot(self, timeslot: int, use_cache: bool = True) -> Dict[str, Any]:
        """
        获取指定时隙的完整数据（带缓存）
        
        Args:
            timeslot: 时隙ID
            use_cache: 是否使用缓存
            
        Returns:
            时隙数据字典
        """
        if timeslot < 0 or timeslot >= self.num_timeslots:
            raise ValueError(f"时隙ID超出范围: {timeslot} (有效范围: 0-{self.num_timeslots-1})")
        
        # 检查缓存
        if use_cache and timeslot in self._timeslot_cache:
            self.stats['cache_hits'] += 1
            # 更新访问顺序
            self._access_order.remove(timeslot)
            self._access_order.append(timeslot)
            return self._timeslot_cache[timeslot]
        
        # 加载数据
        data = self._load_timeslot_npz(timeslot)
        
        # 更新缓存
        if use_cache:
            self._update_cache(timeslot, data)
        
        return data
    
    def _update_cache(self, timeslot: int, data: Dict[str, Any]):
        """更新LRU缓存"""
        # 如果缓存满了，删除最旧的
        if len(self._timeslot_cache) >= self._cache_size:
            oldest = self._access_order.pop(0)
            del self._timeslot_cache[oldest]
        
        # 添加新数据
        self._timeslot_cache[timeslot] = data
        self._access_order.append(timeslot)
    
    def get_satellite_state(self, timeslot: int, satellite_id: int) -> np.ndarray:
        """
        获取指定卫星在指定时隙的状态
        
        Args:
            timeslot: 时隙ID
            satellite_id: 卫星ID
            
        Returns:
            卫星状态数组 [纬度, 经度, 光照, 状态, 区域ID]
        """
        data = self.get_timeslot(timeslot)
        return data['satellite_states'][satellite_id]
    
    def get_satellite_states_batch(self, timeslot: int) -> np.ndarray:
        """
        获取指定时隙所有卫星的状态
        
        Returns:
            形状为 (72, 5) 的状态数组
        """
        data = self.get_timeslot(timeslot)
        return data['satellite_states']
    
    def get_ground_links(self, timeslot: int, satellite_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取地面链路信息
        
        Args:
            timeslot: 时隙ID
            satellite_id: 可选，指定卫星ID
            
        Returns:
            链路信息字典
        """
        data = self.get_timeslot(timeslot)
        
        if satellite_id is not None:
            # 获取特定卫星的链路
            distance_row = data['ground_distance'].getrow(satellite_id)
            uplink_row = data['ground_uplink'].getrow(satellite_id)
            downlink_row = data['ground_downlink'].getrow(satellite_id)
            
            # 获取非零元素（有链路的地面站）
            indices = distance_row.indices
            
            links = []
            for idx, station_id in enumerate(indices):
                links.append({
                    'station_id': int(station_id),
                    'distance_km': float(distance_row.data[idx]),
                    'uplink_mbps': float(uplink_row.data[idx]),
                    'downlink_mbps': float(downlink_row.data[idx])
                })
            
            return {'satellite_id': satellite_id, 'links': links}
        else:
            # 返回所有链路矩阵
            return {
                'distance': data['ground_distance'],
                'uplink': data['ground_uplink'],
                'downlink': data['ground_downlink']
            }
    
    def get_cloud_links(self, timeslot: int, satellite_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取云站链路信息
        
        Args:
            timeslot: 时隙ID
            satellite_id: 可选，指定卫星ID
            
        Returns:
            链路信息字典
        """
        data = self.get_timeslot(timeslot)
        
        if satellite_id is not None:
            distance_row = data['cloud_distance'].getrow(satellite_id)
            s2c_row = data['cloud_s2c'].getrow(satellite_id)
            c2s_row = data['cloud_c2s'].getrow(satellite_id)
            
            indices = distance_row.indices
            
            links = []
            for idx, cloud_id in enumerate(indices):
                links.append({
                    'cloud_id': f'cloud_{cloud_id}',
                    'distance_km': float(distance_row.data[idx]),
                    'sat_to_cloud_mbps': float(s2c_row.data[idx]),
                    'cloud_to_sat_mbps': float(c2s_row.data[idx])
                })
            
            return {'satellite_id': satellite_id, 'links': links}
        else:
            return {
                'distance': data['cloud_distance'],
                'sat_to_cloud': data['cloud_s2c'],
                'cloud_to_sat': data['cloud_c2s']
            }
    
    def get_isl_links(self, timeslot: int, satellite_id: Optional[int] = None) -> Union[List[Dict], sp.csr_matrix]:
        """
        获取星间链路信息
        
        Args:
            timeslot: 时隙ID
            satellite_id: 可选，指定卫星ID
            
        Returns:
            链路列表或完整ISL矩阵
        """
        data = self.get_timeslot(timeslot)
        isl_matrix = data['isl_distance']
        
        if satellite_id is not None:
            row = isl_matrix.getrow(satellite_id)
            indices = row.indices
            
            links = []
            for idx, neighbor_id in enumerate(indices):
                links.append({
                    'neighbor_id': int(neighbor_id),
                    'distance_km': float(row.data[idx])
                })
            
            return links
        else:
            return isl_matrix
    
    def get_tasks(self, timeslot: int, satellite_id: Optional[int] = None, 
                  station_id: Optional[int] = None) -> np.ndarray:
        """
        获取任务数据
        
        Args:
            timeslot: 时隙ID
            satellite_id: 可选，筛选特定卫星的任务
            station_id: 可选，筛选特定地面站的任务
            
        Returns:
            任务数组（结构化数组）
        """
        data = self.get_timeslot(timeslot)
        tasks = data['tasks']
        
        if len(tasks) == 0:
            return tasks
        
        # 应用筛选条件
        mask = np.ones(len(tasks), dtype=bool)
        
        if satellite_id is not None:
            mask &= (tasks['satellite_id'] == satellite_id)
        
        if station_id is not None:
            mask &= (tasks['station_id'] == station_id)
        
        return tasks[mask]
    
    def get_timeslot_as_json(self, timeslot: int) -> Dict[str, Any]:
        """
        将时隙数据转换为与原JSON格式兼容的字典
        
        Args:
            timeslot: 时隙ID
            
        Returns:
            JSON兼容的字典格式
        """
        data = self.get_timeslot(timeslot)
        
        # 构建JSON格式
        json_data = {
            'timeslot_id': data['timeslot_id'],
            'satellites': {}
        }
        
        # 处理每个卫星
        for sat_id in range(self.num_satellites):
            sat_state = data['satellite_states'][sat_id]
            
            # 获取该卫星的所有链路
            ground_links = self.get_ground_links(timeslot, sat_id)['links']
            cloud_links = self.get_cloud_links(timeslot, sat_id)['links']
            isl_links = self.get_isl_links(timeslot, sat_id)
            
            # 获取该卫星的任务
            tasks = self.get_tasks(timeslot, satellite_id=sat_id)
            
            # 构建地面站链路（包含任务）
            visible_ground_stations = []
            for link in ground_links:
                station_id = link['station_id']
                station_tasks = tasks[tasks['station_id'] == station_id]
                
                if len(station_tasks) > 0:
                    station_data = {
                        'station_id': station_id,
                        'distance_km': link['distance_km'],
                        'uplink_mbps': link['uplink_mbps'],
                        'downlink_mbps': link['downlink_mbps'],
                        'tasks': []
                    }
                    
                    for task in station_tasks:
                        station_data['tasks'].append({
                            'task_id': int(task['task_id']),
                            'data_size_mb': float(task['data_size_mb']),
                            'complexity': float(task['complexity']),
                            'priority': int(task['priority']),
                            'deadline': float(task['deadline']),
                            'generation_time': int(task['generation_time'])
                        })
                    
                    visible_ground_stations.append(station_data)
            
            # 构建卫星数据
            json_data['satellites'][str(sat_id)] = {
                'state': sat_state.tolist(),
                'visible_ground_stations': visible_ground_stations,
                'cloud_links': cloud_links,
                'isl_links': isl_links
            }
        
        return json_data
    
    def load_timeslot_range(self, start: int, end: int) -> List[Dict[str, Any]]:
        """
        批量加载时隙范围
        
        Args:
            start: 起始时隙（包含）
            end: 结束时隙（不包含）
            
        Returns:
            时隙数据列表
        """
        results = []
        for timeslot in range(start, min(end, self.num_timeslots)):
            results.append(self.get_timeslot(timeslot))
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取加载器统计信息"""
        avg_load_time = self.stats['total_load_time'] / self.stats['loads'] if self.stats['loads'] > 0 else 0
        cache_hit_rate = self.stats['cache_hits'] / self.stats['loads'] if self.stats['loads'] > 0 else 0
        
        return {
            'total_loads': self.stats['loads'],
            'cache_hits': self.stats['cache_hits'],
            'cache_hit_rate': f"{cache_hit_rate*100:.1f}%",
            'avg_load_time_ms': avg_load_time * 1000,
            'total_load_time_s': self.stats['total_load_time'],
            'cache_size': len(self._timeslot_cache),
            'metadata': self.metadata
        }
    
    def clear_cache(self):
        """清空缓存"""
        self._timeslot_cache.clear()
        self._access_order.clear()
    
    def benchmark(self, num_random_loads: int = 100) -> Dict[str, float]:
        """
        性能基准测试
        
        Args:
            num_random_loads: 随机加载次数
            
        Returns:
            性能指标字典
        """
        import random
        
        print(f"\n开始性能基准测试 ({num_random_loads} 次随机加载)...")
        
        # 清空缓存和统计
        self.clear_cache()
        self.stats = {'loads': 0, 'cache_hits': 0, 'total_load_time': 0.0}
        
        # 顺序加载测试
        start = time.time()
        for i in range(min(10, self.num_timeslots)):
            _ = self.get_timeslot(i)
        sequential_time = time.time() - start
        
        # 随机加载测试
        random_timeslots = [random.randint(0, self.num_timeslots-1) for _ in range(num_random_loads)]
        start = time.time()
        for ts in random_timeslots:
            _ = self.get_timeslot(ts)
        random_time = time.time() - start
        
        # 缓存命中测试
        cached_start = time.time()
        for _ in range(10):
            _ = self.get_timeslot(0)  # 重复加载同一时隙
        cached_time = time.time() - cached_start
        
        stats = self.get_statistics()
        
        return {
            'sequential_10_slots_ms': sequential_time * 1000,
            'random_loads_ms': random_time * 1000,
            'avg_random_load_ms': (random_time / num_random_loads) * 1000,
            'cached_10_loads_ms': cached_time * 1000,
            'cache_hit_rate': stats['cache_hit_rate'],
            'avg_load_time_ms': stats['avg_load_time_ms']
        }


def demo():
    """演示数据加载器的使用"""
    print("NPZ数据加载器演示")
    print("=" * 60)
    
    # 创建加载器
    loader = NPZDataLoader('src/data/npz_data')
    
    # 显示元数据
    print("\n系统参数:")
    print(f"  - 时隙数量: {loader.num_timeslots}")
    print(f"  - 卫星数量: {loader.num_satellites}")
    print(f"  - 地面站数量: {loader.num_ground_stations}")
    print(f"  - 云站数量: {loader.num_cloud_stations}")
    
    # 加载示例时隙
    print("\n加载时隙 0...")
    timeslot_0 = loader.get_timeslot(0)
    
    # 显示卫星0的状态
    sat_0_state = loader.get_satellite_state(0, 0)
    print(f"\n卫星 0 在时隙 0 的状态:")
    print(f"  - 位置: ({sat_0_state[0]:.2f}, {sat_0_state[1]:.2f})")
    print(f"  - 光照: {'是' if sat_0_state[2] else '否'}")
    print(f"  - 运行: {'正常' if sat_0_state[3] else '异常'}")
    print(f"  - 区域: {int(sat_0_state[4])}")
    
    # 显示链路信息
    ground_links = loader.get_ground_links(0, 0)
    print(f"\n卫星 0 的地面链路: {len(ground_links['links'])} 个")
    
    cloud_links = loader.get_cloud_links(0, 0)
    print(f"卫星 0 的云链路: {len(cloud_links['links'])} 个")
    
    isl_links = loader.get_isl_links(0, 0)
    print(f"卫星 0 的ISL链路: {len(isl_links)} 个")
    
    # 显示任务信息
    tasks = loader.get_tasks(0, satellite_id=0)
    print(f"\n卫星 0 的任务数: {len(tasks)}")
    
    # 性能测试
    print("\n" + "=" * 60)
    print("性能基准测试:")
    print("-" * 40)
    
    benchmark_results = loader.benchmark(100)
    
    print(f"  - 顺序加载10个时隙: {benchmark_results['sequential_10_slots_ms']:.2f} ms")
    print(f"  - 随机加载100次平均: {benchmark_results['avg_random_load_ms']:.2f} ms")
    print(f"  - 缓存命中率: {benchmark_results['cache_hit_rate']}")
    print(f"  - 缓存加载10次: {benchmark_results['cached_10_loads_ms']:.2f} ms")
    
    # 统计信息
    stats = loader.get_statistics()
    print(f"\n统计信息:")
    print(f"  - 总加载次数: {stats['total_loads']}")
    print(f"  - 缓存命中次数: {stats['cache_hits']}")
    print(f"  - 平均加载时间: {stats['avg_load_time_ms']:.2f} ms")
    
    print("\n演示完成！")


if __name__ == '__main__':
    demo()