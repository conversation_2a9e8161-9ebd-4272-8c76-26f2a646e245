"""
数据整合程序：将所有仿真数据整合为统一的JSON格式
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any
import re

class DataIntegrator:
    def __init__(self):
        """初始化数据整合器"""
        self.satellite_data = None
        self.ground_stations = None
        self.cloud_stations = None
        self.tasks_data = None
        self.link_rates = None
        self.regions = None
        self.satellite_regions = {}
        self.isl_links = {}
        self.task_assignments = {}  # 记录任务分配情况
        
    def load_all_data(self):
        """加载所有数据文件"""
        print("加载数据文件...")
        
        # 1. 加载卫星轨道数据
        self.satellite_data = pd.read_csv('env_data/satellite_data_72_0.csv')
        print(f"  - 卫星数据: {len(self.satellite_data)} 条记录")
        
        # 2. 加载地面站数据
        self.ground_stations = pd.read_csv('env_data/global_ground_stations.csv')
        print(f"  - 地面站: {len(self.ground_stations)} 个")
        
        # 3. 加载云站数据
        self.cloud_stations = pd.read_csv('env_data/cloud_station.csv')
        print(f"  - 云站: {len(self.cloud_stations)} 个")
        
        # 4. 加载任务数据
        self.tasks_data = pd.read_csv('env_data/generated_tasks.csv')
        print(f"  - 任务数据: {len(self.tasks_data)} 条")
        
        # 5. 加载链路速率数据
        with open('env_data/link_rates_detailed.json', 'r') as f:
            self.link_rates = json.load(f)
        print(f"  - 链路数据: {len(self.link_rates['timeslots'])} 个时隙")
        
        # 6. 加载区域定义
        with open('env_data/regions.json', 'r') as f:
            self.regions = json.load(f)
        print(f"  - 区域定义: {len(self.regions)} 个")
        
        # 7. 解析卫星区域数据
        self._parse_satellite_regions()
        
        # 8. 解析星间链路数据
        self._parse_isl_links()
        
    def _parse_satellite_regions(self):
        """解析卫星区域数据"""
        try:
            with open('env_data/satellite_regions_output.txt', 'r', encoding='utf-8') as f:
                content = f.read()
            
            current_timeslot = None
            for line in content.split('\n'):
                if '时隙' in line:
                    # 提取时隙号
                    match = re.search(r'时隙\s+(\d+)', line)
                    if match:
                        current_timeslot = int(match.group(1))
                        if current_timeslot not in self.satellite_regions:
                            self.satellite_regions[current_timeslot] = {}
                elif '卫星' in line and current_timeslot is not None:
                    # 提取卫星ID和区域ID
                    match = re.search(r'卫星\s+(\d+):\s+区域\s+(\w+)', line)
                    if match:
                        sat_id = int(match.group(1))
                        region = match.group(2)
                        if region != 'None':
                            self.satellite_regions[current_timeslot][sat_id] = int(region)
                        else:
                            self.satellite_regions[current_timeslot][sat_id] = None
        except Exception as e:
            print(f"  警告: 无法解析卫星区域数据 - {e}")
    
    def _parse_isl_links(self):
        """解析星间链路数据 - 使用流式读取避免内存问题"""
        try:
            print(f"  - 开始解析ISL链路数据...")
            
            with open('env_data/satellite_analysis_1500_timeslots.txt', 'r', encoding='utf-8') as f:
                current_timeslot = None
                current_sat = None
                in_sat_links = False
                line_count = 0
                
                for line in f:
                    line = line.strip()
                    line_count += 1
                    
                    # 每100000行输出一次进度
                    if line_count % 100000 == 0:
                        print(f"    已处理 {line_count} 行...")
                    
                    # 找到新的时隙
                    if '========== 时隙' in line:
                        match = re.search(r'时隙\s+(\d+)', line)
                        if match:
                            current_timeslot = int(match.group(1))  # 时隙从0开始
                            if current_timeslot not in self.isl_links:
                                self.isl_links[current_timeslot] = {}
                            current_sat = None
                            in_sat_links = False
                    
                    # 找到卫星ID
                    elif line.startswith('卫星 ') and ':' in line:
                        match = re.search(r'卫星\s+(\d+):', line)
                        if match and current_timeslot is not None:
                            current_sat = int(match.group(1))
                            self.isl_links[current_timeslot][current_sat] = []
                            in_sat_links = False
                    
                    # 找到距离最近的卫星部分
                    elif '距离最近的' in line and current_sat is not None:
                        in_sat_links = True
                    
                    # 解析ISL链路
                    elif in_sat_links and '卫星' in line:
                        match = re.search(r'卫星(\d+):\s+([\d.]+)\s+km', line)
                        if match and current_timeslot is not None and current_sat is not None:
                            neighbor_id = int(match.group(1))
                            distance = float(match.group(2))
                            # 保留所有卫星链路
                            self.isl_links[current_timeslot][current_sat].append({
                                'neighbor_id': neighbor_id,
                                'distance_km': distance
                            })
                    
                    # 遇到可见地面用户站点就停止ISL解析
                    elif '可见地面用户站点' in line:
                        in_sat_links = False
            
            # 输出解析结果统计
            parsed_timeslots = sorted(self.isl_links.keys())
            if parsed_timeslots:
                print(f"  - ISL链路解析完成: 时隙 {parsed_timeslots[0]} 到 {parsed_timeslots[-1]}")
                print(f"  - 总共解析了 {len(parsed_timeslots)} 个时隙的ISL数据")
                        
        except Exception as e:
            print(f"  警告: 无法解析ISL链路数据 - {e}")
    
    def get_satellite_state(self, timeslot: int, sat_id: int) -> List:
        """获取卫星状态 [纬度, 经度, 光照, 运行状态, 区域ID]"""
        # 从satellite_data获取基本信息
        sat_row = self.satellite_data[
            (self.satellite_data['卫星ID'] == sat_id) & 
            (self.satellite_data['时隙'] == timeslot + 1)  # 时隙从1开始
        ]
        
        if sat_row.empty:
            return None
        
        row = sat_row.iloc[0]
        
        # 获取区域ID
        region_id = None
        if timeslot + 1 in self.satellite_regions:  # 时隙从1开始
            region_id = self.satellite_regions[timeslot + 1].get(sat_id)
        
        return [
            float(row['纬度']),
            float(row['经度']),
            1.0 if row['光照'] else 0.0,
            1.0 if row['状态'] else 0.0,
            region_id
        ]
    
    def assign_tasks_for_timeslot(self, timeslot: int):
        """为指定时隙分配任务到卫星
        采用简单的轮询分配策略：将地面站的任务分配给第一个可见的卫星
        """
        if timeslot not in self.task_assignments:
            self.task_assignments[timeslot] = {}
        
        # 获取该时隙的所有任务
        timeslot_tasks = self.tasks_data[self.tasks_data['timeslot'] == timeslot]
        
        # 获取该时隙的链路数据
        timeslot_links = self.link_rates['timeslots'].get(str(timeslot), {})
        
        # 为每个任务找到第一个可见的卫星
        for _, task in timeslot_tasks.iterrows():
            station_id = int(task['location_id'])
            
            # 遍历所有卫星，找到第一个能看到该地面站的卫星
            for sat_id in range(72):
                sat_links = timeslot_links.get('satellites', {}).get(str(sat_id), {})
                ground_links = sat_links.get('ground_links', {})
                
                # 如果该卫星能看到该地面站
                if str(station_id) in ground_links:
                    # 将任务分配给该卫星
                    if sat_id not in self.task_assignments[timeslot]:
                        self.task_assignments[timeslot][sat_id] = {}
                    if station_id not in self.task_assignments[timeslot][sat_id]:
                        self.task_assignments[timeslot][sat_id][station_id] = []
                    
                    # 只包含模板中要求的字段
                    task_dict = {
                        'task_id': int(task['task_id']),
                        'data_size_mb': float(task['data_size_mb']),
                        'complexity': float(task['complexity_cycles_per_bit']),
                        'priority': int(task['priority']),
                        'deadline': float(task['deadline_timestamp']),
                        'generation_time': timeslot
                    }
                    self.task_assignments[timeslot][sat_id][station_id].append(task_dict)
                    break  # 任务已分配，跳出卫星循环
    
    def get_tasks_for_station_and_satellite(self, timeslot: int, station_id: int, sat_id: int) -> List[Dict]:
        """获取指定地面站为指定卫星生成的任务"""
        if timeslot in self.task_assignments:
            if sat_id in self.task_assignments[timeslot]:
                return self.task_assignments[timeslot][sat_id].get(station_id, [])
        return []
    
    def integrate_timeslot(self, timeslot: int) -> Dict:
        """整合单个时隙的所有数据"""
        # 首先分配该时隙的任务
        self.assign_tasks_for_timeslot(timeslot)
        
        timeslot_data = {
            'timeslot_id': timeslot,
            'satellites': {}
        }
        
        # 获取该时隙的链路数据
        timeslot_links = self.link_rates['timeslots'].get(str(timeslot), {})
        
        # 处理每颗卫星
        for sat_id in range(72):
            # 获取卫星状态
            state = self.get_satellite_state(timeslot, sat_id)
            if state is None:
                continue
            
            sat_data = {
                'state': state,
                'visible_ground_stations': [],
                'cloud_links': [],
                'isl_links': []
            }
            
            # 获取该卫星的链路信息
            sat_links = timeslot_links.get('satellites', {}).get(str(sat_id), {})
            
            # 处理地面链路
            ground_links = sat_links.get('ground_links', {})
            num_visible_stations = len(ground_links)  # 可见地面站数量
            
            for station_id_str, link_info in ground_links.items():
                station_id = int(station_id_str)
                
                # 获取该地面站分配给该卫星的任务
                tasks = self.get_tasks_for_station_and_satellite(timeslot, station_id, sat_id)
                
                # 只保留有任务的地面站
                if tasks:
                    station_data = {
                        'station_id': station_id,
                        'distance_km': round(link_info['distance_km'], 2),
                        # 速率除以可见地面站数量
                        'uplink_mbps': round(link_info['uplink_mbps'] / num_visible_stations, 2),
                        'downlink_mbps': round(link_info['downlink_mbps'] / num_visible_stations, 2),
                        'tasks': tasks
                    }
                    sat_data['visible_ground_stations'].append(station_data)
            
            # 处理云链路
            cloud_links = sat_links.get('cloud_links', {})
            for cloud_id, link_info in cloud_links.items():
                cloud_data = {
                    'cloud_id': cloud_id,
                    'distance_km': round(link_info['distance_km'], 2),
                    'sat_to_cloud_mbps': round(link_info['sat_to_cloud_mbps'], 2),
                    'cloud_to_sat_mbps': round(link_info['cloud_to_sat_mbps'], 2)
                }
                sat_data['cloud_links'].append(cloud_data)
            
            # 处理ISL链路
            if timeslot in self.isl_links:
                isl_data = self.isl_links[timeslot].get(sat_id, [])
                sat_data['isl_links'] = isl_data  # 保留所有ISL链路
            
            timeslot_data['satellites'][str(sat_id)] = sat_data
        
        return timeslot_data
    
    def integrate_all_timeslots(self, max_timeslots: int = 1500):
        """整合所有时隙数据"""
        print(f"\n开始整合数据（前{max_timeslots}个时隙）...")
        
        integrated_data = []
        
        for timeslot in range(max_timeslots):
            print(f"  处理时隙 {timeslot}...")
            timeslot_data = self.integrate_timeslot(timeslot)
            integrated_data.append(timeslot_data)
        
        return integrated_data
    
    def save_integrated_data(self, data: List[Dict], output_file: str = 'integrated_data.json'):
        """保存整合后的数据"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"\n数据已保存到: {output_file}")
        
        # 统计信息
        print(f"  - 总时隙数: {len(data)}")
        if data:
            print(f"  - 每时隙卫星数: {len(data[0]['satellites'])}")
            
            # 统计第一个时隙的任务数
            total_tasks = 0
            for sat_data in data[0]['satellites'].values():
                for station in sat_data['visible_ground_stations']:
                    total_tasks += len(station['tasks'])
            print(f"  - 时隙0总任务数: {total_tasks}")


def main():
    """主函数"""
    integrator = DataIntegrator()
    
    # 加载所有数据
    integrator.load_all_data()
    
    # 整合数据（这里先处理前10个时隙作为示例）
    integrated_data = integrator.integrate_all_timeslots(max_timeslots=1500)
    
    # 保存数据
    integrator.save_integrated_data(integrated_data)
    
    # 也可以保存完整的1500个时隙
    # integrated_data_full = integrator.integrate_all_timeslots(max_timeslots=1500)
    # integrator.save_integrated_data(integrated_data_full, 'integrated_data_full.json')


if __name__ == '__main__':
    main()